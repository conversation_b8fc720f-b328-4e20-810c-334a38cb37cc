// Structured data interfaces for employee information
export interface DepartmentInfo {
  employeeId?: string;
  department?: string;
  designation?: string;
  supervisor?: string;
  workLocation?: string;
}

export interface AddressInfo {
  presentAddress?: string;
  presentCountry?: string;
  presentState?: string;
  presentCity?: string;
  presentPostalCode?: string;
  permanentAddress?: string;
  permanentCountry?: string;
  permanentState?: string;
  permanentCity?: string;
  permanentPostalCode?: string;
}

export interface EmergencyContactInfo {
  emergencyContactType?: string;
  emergencyContactName?: string;
  emergencyContactRelation?: string;
  emergencyContactPhone?: string;
  emergencyContactEmail?: string;
  emergencyContactAddress?: string;
}

export interface IdentityInfo {
  type?: string;
  country?: string;
  number?: string;
  issueDate?: string;
  expiryDate?: string;
}

export interface BankAccountInfo {
  accountHolderName?: string;
  accountNumber?: string;
  bankName?: string;
  branchName?: string;
  routingNumber?: string;
  accountType?: string;
}

export interface SocialLinksInfo {
  linkedinUrl?: string;
  twitterUrl?: string;
  facebookUrl?: string;
  instagramUrl?: string;
}

export interface CreateEmployeeRequest {
  // User creation fields
  email: string;
  password?: string;

  // Personal Information
  firstName: string;
  lastName: string;
  phone?: string;
  dateOfBirth?: string;
  bloodGroup?: string;
  gender?: string;
  nationality?: string;
  maritalStatus?: string;

  // Employment Information
  joiningDate?: string;
  jobType?: string;
  jobStatus?: string;

  // Structured data
  departmentInfo?: DepartmentInfo;
  presentAddress?: AddressInfo;
  permanentAddress?: AddressInfo;
  emergencyContact?: EmergencyContactInfo;
  identityInfo?: IdentityInfo[];
  bankAccount?: BankAccountInfo;
  socialLinks?: SocialLinksInfo;

  // Organization context (extracted from user token)
  organizationId: bigint;

  // Audit fields
  requestUserId: bigint;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface UpdateEmployeeRequest {
  id: bigint;

  // Personal Information
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  dateOfBirth?: string;
  bloodGroup?: string;
  gender?: string;
  nationality?: string;
  maritalStatus?: string;

  // Employment Information
  joiningDate?: string;
  jobType?: string;
  jobStatus?: string;

  // Structured data
  departmentInfo?: DepartmentInfo;
  presentAddress?: AddressInfo;
  permanentAddress?: AddressInfo;
  emergencyContact?: EmergencyContactInfo;
  identityInfo?: IdentityInfo[];
  bankAccount?: BankAccountInfo;
  socialLinks?: SocialLinksInfo;

  // Audit fields
  requestUserId: bigint;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface GetEmployeeRequest {
  id: bigint;
  organizationId: bigint;
  requestUserId: bigint;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface ListEmployeesRequest {
  page?: number;
  limit?: number;
  search?: string;
  jobType?: string;
  jobStatus?: string;
  organizationId: bigint;
  requestUserId: bigint;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface RemoveEmployeeRequest {
  id: bigint;
  organizationId: bigint;
  requestUserId: bigint;
  roleName: string;
  ipAddress: string;
  userAgent: string;
}

export interface EmployeeResponse {
  success: boolean;
  message: string;
  employee?: EmployeeInfo;
}

export interface ListEmployeesResponse {
  success: boolean;
  message: string;
  employees: EmployeeInfo[];
  total: number;
  page: number;
  limit: number;
}

export interface RemoveEmployeeResponse {
  success: boolean;
  message: string;
}

export interface EmployeeInfo {
  userId: bigint;
  email?: string;

  // Personal Information
  firstName?: string;
  lastName?: string;
  phone?: string;
  dateOfBirth?: string;
  bloodGroup?: string;
  gender?: string;
  nationality?: string;
  maritalStatus?: string;

  // Employment Information
  joiningDate?: string;
  jobType?: string;
  jobStatus?: string;

  // Department Information
  employeeId?: string;
  department?: string;
  designation?: string;
  supervisor?: string;
  workLocation?: string;

  // Address Information
  presentAddress?: string;
  presentCountry?: string;
  presentState?: string;
  presentCity?: string;
  presentPostalCode?: string;
  permanentAddress?: string;
  permanentCountry?: string;
  permanentState?: string;
  permanentCity?: string;
  permanentPostalCode?: string;

  // Emergency Contact
  emergencyContactType?: string;
  emergencyContactName?: string;
  emergencyContactRelation?: string;
  emergencyContactPhone?: string;
  emergencyContactEmail?: string;
  emergencyContactAddress?: string;

  // Identity Information
  identityType?: string;
  identityCountry?: string;
  identityNumber?: string;
  identityIssueDate?: string;
  identityExpiryDate?: string;

  // Bank Account Information
  accountHolderName?: string;
  accountNumber?: string;
  bankName?: string;
  branchName?: string;
  routingNumber?: string;
  accountType?: string;

  // Social Profile
  linkedinUrl?: string;
  twitterUrl?: string;
  facebookUrl?: string;
  instagramUrl?: string;

  // Organization
  organizationId?: bigint;

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

export interface EmployeeAddressInfo {
  id?: bigint;
  userId: bigint;
  addressType: string;
  addressLine?: string;
  country?: string;
  state?: string;
  city?: string;
  postalCode?: string;
}

export interface EmployeeEmergencyContactInfo {
  id?: bigint;
  userId: bigint;
  category?: string;
  name?: string;
  relationship?: string;
  address?: string;
  phoneNumber?: string;
  email?: string;
}

export interface EmployeeIdentityDocInfo {
  id?: bigint;
  userId: bigint;
  docType?: string;
  number?: string;
  nationality?: string;
  issueDate?: string;
  expiryDate?: string;
}

export interface EmployeeBankAccountInfo {
  id?: bigint;
  userId: bigint;
  accountHolder?: string;
  accountNumber?: string;
  bankName?: string;
  branchName?: string;
}

// Internal service interfaces
export interface CreateEmployeeData {
  userId: bigint;

  // Personal Information
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  dateOfBirth?: Date;
  bloodGroup?: string;
  gender?: string;
  nationality?: string;
  maritalStatus?: string;

  // Employment Information
  joiningDate?: Date;
  jobType?: string;
  jobStatus?: string;

  // Department Information
  employeeId?: string;
  department?: string;
  designation?: string;
  supervisor?: string;
  workLocation?: string;

  // Address Information
  presentAddress?: string;
  presentCountry?: string;
  presentState?: string;
  presentCity?: string;
  presentPostalCode?: string;
  permanentAddress?: string;
  permanentCountry?: string;
  permanentState?: string;
  permanentCity?: string;
  permanentPostalCode?: string;

  // Emergency Contact
  emergencyContactType?: string;
  emergencyContactName?: string;
  emergencyContactRelation?: string;
  emergencyContactPhone?: string;
  emergencyContactEmail?: string;
  emergencyContactAddress?: string;

  // Identity Information
  identityType?: string;
  identityCountry?: string;
  identityNumber?: string;
  identityIssueDate?: string;
  identityExpiryDate?: string;

  // Bank Account Information
  accountHolderName?: string;
  accountNumber?: string;
  bankName?: string;
  branchName?: string;
  routingNumber?: string;
  accountType?: string;

  // Social Profile
  linkedinUrl?: string;
  twitterUrl?: string;
  facebookUrl?: string;
  instagramUrl?: string;

  // Legacy fields
  agencyId?: bigint;
  orgId?: string;
}

export interface UpdateEmployeeData {
  // Personal Information
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  dateOfBirth?: Date;
  bloodGroup?: string;
  gender?: string;
  nationality?: string;
  maritalStatus?: string;

  // Employment Information
  joiningDate?: Date;
  jobType?: string;
  jobStatus?: string;

  // Department Information
  employeeId?: string;
  department?: string;
  designation?: string;
  supervisor?: string;
  workLocation?: string;

  // Address Information
  presentAddress?: string;
  presentCountry?: string;
  presentState?: string;
  presentCity?: string;
  presentPostalCode?: string;
  permanentAddress?: string;
  permanentCountry?: string;
  permanentState?: string;
  permanentCity?: string;
  permanentPostalCode?: string;

  // Emergency Contact
  emergencyContactType?: string;
  emergencyContactName?: string;
  emergencyContactRelation?: string;
  emergencyContactPhone?: string;
  emergencyContactEmail?: string;
  emergencyContactAddress?: string;

  // Identity Information
  identityType?: string;
  identityCountry?: string;
  identityNumber?: string;
  identityIssueDate?: string;
  identityExpiryDate?: string;

  // Bank Account Information
  accountHolderName?: string;
  accountNumber?: string;
  bankName?: string;
  branchName?: string;
  routingNumber?: string;
  accountType?: string;

  // Social Profile
  linkedinUrl?: string;
  twitterUrl?: string;
  facebookUrl?: string;
  instagramUrl?: string;

  // Legacy fields
  agencyId?: bigint;
  orgId?: string;
}

export interface EmployeeFilters {
  search?: string;
  jobType?: string;
  jobStatus?: string;
  agencyId?: bigint;
  orgId?: string;
}

export interface PaginationOptions {
  page: number;
  limit: number;
  offset: number;
}

export const JOB_TYPES = [
  'full-time',
  'part-time',
  'contract',
  'intern',
  'consultant',
  'freelance',
] as const;

export const JOB_STATUSES = [
  'active',
  'inactive',
  'on-leave',
  'terminated',
  'resigned',
] as const;

export const BLOOD_GROUPS = [
  'A+',
  'A-',
  'B+',
  'B-',
  'AB+',
  'AB-',
  'O+',
  'O-',
] as const;

export const GENDERS = [
  'male',
  'female',
  'other',
  'prefer-not-to-say',
] as const;

export type JobType = (typeof JOB_TYPES)[number];
export type JobStatus = (typeof JOB_STATUSES)[number];
export type BloodGroup = (typeof BLOOD_GROUPS)[number];
export type Gender = (typeof GENDERS)[number];
