import {
  Injectable,
  NotFoundException,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op, UniqueConstraintError } from 'sequelize';
import { firstValueFrom } from 'rxjs';
import { EmployeePersonal } from './models/employee-personal.model';
import { EmployeeAddress } from './models/employee-address.model';
import { EmployeeEmergencyContact } from './models/employee-emergency-contact.model';
import { EmployeeIdentityDoc } from './models/employee-identity-doc.model';
import { EmployeeBankAccount } from './models/employee-bank-account.model';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { AuditClientService } from '../audit/audit.service';
import { AppService } from '../app.service';
import {
  CreateEmployeeRequest,
  UpdateEmployeeRequest,
  GetEmployeeRequest,
  ListEmployeesRequest,
  RemoveEmployeeRequest,
  EmployeeResponse,
  ListEmployeesResponse,
  RemoveEmployeeResponse,
  EmployeeInfo,
  CreateEmployeeData,
  UpdateEmployeeData,
  EmployeeAddressInfo,
  EmployeeEmergencyContactInfo,
  EmployeeIdentityDocInfo,
  EmployeeBankAccountInfo,
  JOB_TYPES,
  JOB_STATUSES,
  BLOOD_GROUPS,
  GENDERS,
} from './employee.interface';
import { CreateAuditLogRequest } from '../auth/auth.interfaces';
import { User } from '../user/model/user.model';
import { Role } from '../user/model/role.model';
import { UserRole } from '../user/model/user-role.model';
import { Department } from '../user/model/department.model';
import { UserDepartment } from '../user/model/user‐department.model';
import { Organization } from '../organization/organization.model';
import * as bcrypt from 'bcrypt';

@Injectable()
export class EmployeeService {
  private readonly logger = new Logger(EmployeeService.name);

  constructor(
    @InjectModel(EmployeePersonal)
    private readonly employeeModel: typeof EmployeePersonal,
    @InjectModel(EmployeeAddress)
    private readonly employeeAddressModel: typeof EmployeeAddress,
    @InjectModel(EmployeeEmergencyContact)
    private readonly employeeEmergencyContactModel: typeof EmployeeEmergencyContact,
    @InjectModel(EmployeeIdentityDoc)
    private readonly employeeIdentityDocModel: typeof EmployeeIdentityDoc,
    @InjectModel(EmployeeBankAccount)
    private readonly employeeBankAccountModel: typeof EmployeeBankAccount,
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(Role)
    private readonly roleModel: typeof Role,
    @InjectModel(UserRole)
    private readonly userRoleModel: typeof UserRole,
    @InjectModel(Department)
    private readonly departmentModel: typeof Department,
    @InjectModel(UserDepartment)
    private readonly userDepartmentModel: typeof UserDepartment,
    @InjectModel(Organization)
    private readonly organizationModel: typeof Organization,
    private readonly auditService: AuditClientService,
    private readonly appService: AppService
  ) {}

  // Audit-log helper, mirroring AuthService
  private async createAuditLog(payload: CreateAuditLogRequest) {
    const start = Date.now();
    try {
      await firstValueFrom(this.auditService.createAuditLog(payload));
      this.appService.trackAuditLogRequest(
        'create',
        'success',
        (Date.now() - start) / 1000
      );
    } catch (error) {
      this.appService.trackAuditLogRequest(
        'create',
        'error',
        (Date.now() - start) / 1000
      );
      this.logger.error('Failed to create audit log', error);
    }
  }

  // Metrics tracking helper
  private trackMetrics(
    operation: string,
    status: 'success' | 'failure' | 'error',
    duration: number
  ) {
    try {
      this.appService.trackAuthorization('employee', operation, status);
    } catch (err) {
      this.logger.warn(`Metrics error: ${err.message}`);
    }
  }

  async createEmployee(
    request: CreateEmployeeRequest
  ): Promise<EmployeeResponse> {
    const startTime = Date.now();
    let transaction: any;

    try {
      // Log the full request for debugging
      this.logger.debug(
        `CreateEmployee request received: ${JSON.stringify(request, null, 2)}`
      );

      // Validate required fields
      if (!request.email) {
        this.logger.error('Email is required but not provided in request');
        this.logger.error(`Request keys: ${Object.keys(request).join(', ')}`);
        return {
          success: false,
          message: 'Email is required',
        };
      }

      this.logger.log(`Creating employee with email: ${request.email}`);

      // Start transaction
      transaction = await this.userModel.sequelize.transaction();

      // 1. Check if email already exists
      const existingUser = await this.userModel.findOne({
        where: { email: request.email },
        transaction,
      });

      if (existingUser) {
        await transaction.rollback();
        return {
          success: false,
          message: 'Email already exists',
        };
      }

      // 2. Validate job type and status
      if (request.jobType && !JOB_TYPES.includes(request.jobType as any)) {
        await transaction.rollback();
        throw new BadRequestException(
          `Invalid job type. Must be one of: ${JOB_TYPES.join(', ')}`
        );
      }
      if (
        request.jobStatus &&
        !JOB_STATUSES.includes(request.jobStatus as any)
      ) {
        await transaction.rollback();
        throw new BadRequestException(
          `Invalid job status. Must be one of: ${JOB_STATUSES.join(', ')}`
        );
      }

      // 3. Find or create organization
      let organization = await this.organizationModel.findOne({
        where: { name: request.organizationName || 'ApplyGoal' },
        transaction,
      });

      if (!organization) {
        organization = await this.organizationModel.create(
          {
            name: request.organizationName || 'ApplyGoal',
            description: `Organization for ${
              request.organizationName || 'ApplyGoal'
            }`,
          },
          { transaction }
        );
      }

      // 4. Find department
      let department = null;
      if (request.departmentName) {
        department = await this.departmentModel.findOne({
          where: { name: request.departmentName },
          transaction,
        });
      }

      // 5. Find employee role (organization-specific or system role)
      const employeeRoleName = request.employeeRoleName || 'Employee';

      // First try to find organization-specific role
      let role = await this.roleModel.findOne({
        where: {
          name: employeeRoleName,
          organizationId: organization.id,
        },
        transaction,
      });

      // If not found, try to find system role
      if (!role) {
        role = await this.roleModel.findOne({
          where: {
            name: employeeRoleName,
            isSystemRole: true,
            organizationId: null,
          },
          transaction,
        });
      }

      // If still not found, create organization-specific role
      if (!role) {
        this.logger.log(
          `Creating organization-specific role '${employeeRoleName}' for organization '${organization.name}'`
        );
        role = await this.roleModel.create(
          {
            name: employeeRoleName,
            organizationId: organization.id,
            description: `${employeeRoleName} role for ${organization.name}`,
            isSystemRole: false,
          },
          { transaction }
        );
      }

      // 6. Create user
      const hashedPassword = request.password
        ? await bcrypt.hash(request.password, 10)
        : await bcrypt.hash('DefaultPassword123!', 10); // Default password

      const user = await this.userModel.create(
        {
          email: request.email,
          password: hashedPassword,
          name:
            request.name ||
            `${request.firstName || ''} ${request.lastName || ''}`.trim(),
          phone: request.phone,
          status: 'active',
          organizationId: organization.id,
        },
        { transaction }
      );

      // 7. Assign role to user
      await this.userRoleModel.create(
        {
          userId: user.id,
          roleId: role.id,
        },
        { transaction }
      );

      // 8. Assign department to user (if provided)
      if (department) {
        await this.userDepartmentModel.create(
          {
            userId: user.id,
            departmentId: department.id,
          },
          { transaction }
        );
      }

      // 9. Create employee record with comprehensive data mapping
      const employeeData: CreateEmployeeData = {
        userId: BigInt(user.id),

        // Personal Information
        lastName: request.lastName,
        firstName: request.firstName,
        phone: request.phone,
        dateOfBirth: request.dateOfBirth
          ? new Date(request.dateOfBirth)
          : undefined,
        bloodGroup: request.bloodGroup,
        gender: request.gender,
        nationality: request.nationality,
        maritalStatus: request.maritalStatus,

        // Employment Information
        joiningDate: request.joiningDate
          ? new Date(request.joiningDate)
          : new Date(), // Default to today
        jobType: request.jobType || 'full-time',
        jobStatus: request.jobStatus || 'active',

        // Department Information - prioritize nested structure
        employeeId: request.departmentInfo?.employeeId || request.employeeId,
        department: request.departmentInfo?.department || request.department,
        designation: request.departmentInfo?.designation || request.designation,
        supervisor: request.departmentInfo?.supervisor,
        workLocation:
          request.departmentInfo?.workLocation || request.workLocation,

        // Address Information - prioritize nested structure
        presentAddress:
          request.presentAddress?.presentAddress || request.presentAddressFlat,
        presentCountry:
          request.presentAddress?.presentCountry || request.presentCountry,
        presentState:
          request.presentAddress?.presentState || request.presentState,
        presentCity: request.presentAddress?.presentCity || request.presentCity,
        presentPostalCode:
          request.presentAddress?.presentPostalCode ||
          request.presentPostalCode,
        permanentAddress:
          request.permanentAddress?.permanentAddress ||
          request.permanentAddressFlat,
        permanentCountry:
          request.permanentAddress?.permanentCountry ||
          request.permanentCountry,
        permanentState:
          request.permanentAddress?.permanentState ||
          request.permanentStateFlat,
        permanentCity:
          request.permanentAddress?.permanentCity || request.permanentCityFlat,
        permanentPostalCode:
          request.permanentAddress?.permanentPostalCode ||
          request.permanentPostalCode,

        // Emergency Contact - prioritize nested structure
        emergencyContactType: request.emergencyContact?.emergencyContactType,
        emergencyContactName:
          request.emergencyContact?.emergencyContactName ||
          request.emergencyContactName,
        emergencyContactRelation:
          request.emergencyContact?.emergencyContactRelation ||
          request.emergencyContactRelation,
        emergencyContactPhone:
          request.emergencyContact?.emergencyContactPhone ||
          request.emergencyContactPhone,
        emergencyContactEmail:
          request.emergencyContact?.emergencyContactEmail ||
          request.emergencyContactEmail,
        emergencyContactAddress:
          request.emergencyContact?.emergencyContactAddress ||
          request.emergencyContactAddress,

        // Identity Information - prioritize nested structure
        identityType: request.identityInfo?.type,
        identityCountry: request.identityInfo?.country,
        identityNumber: request.identityInfo?.number,
        identityIssueDate: request.identityInfo?.issueDate,
        identityExpiryDate: request.identityInfo?.expiryDate,

        // Bank Account Information - prioritize nested structure
        accountHolderName:
          request.bankAccount?.accountHolderName || request.accountHolderName,
        accountNumber:
          request.bankAccount?.accountNumber || request.accountNumber,
        bankName: request.bankAccount?.bankName || request.bankName,
        branchName: request.bankAccount?.branchName || request.branchName,
        routingNumber:
          request.bankAccount?.routingNumber || request.routingNumber,
        accountType: request.bankAccount?.accountType || request.accountType,

        // Social Profile - prioritize nested structure
        linkedinUrl: request.socialLinks?.linkedinUrl || request.linkedinUrl,
        twitterUrl: request.socialLinks?.twitterUrl || request.twitterUrl,
        facebookUrl: request.socialLinks?.facebookUrl || request.facebookUrl,
        instagramUrl: request.socialLinks?.instagramUrl || request.instagramUrl,

        // Legacy fields
        agencyId: request.agencyId,
        orgId: request.orgId || organization.id.toString(),
      };

      const employee = await this.employeeModel.create(employeeData as any, {
        transaction,
      });

      // 11. Commit transaction before creating related records
      await transaction.commit();

      // 12. Create related records from both nested structure and legacy arrays
      await this.createRelatedEmployeeRecords(BigInt(employee.userId), request);

      // Audit-log
      await this.createAuditLog({
        userId: Number(request.requestUserId),
        userRole: request.roleName,
        actions: 'CREATE_EMPLOYEE',
        serviceName: 'auth-service',
        resourceType: 'Employee',
        resourceId: Number(employee.userId),
        description: `Created employee for user: ${employee.userId}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });

      this.logger.log(`Employee created successfully: ${employee.userId}`);

      // Fetch the complete employee with all relations
      const fullEmployee = await this.employeeModel.findByPk(employee.userId, {
        include: { all: true, nested: true },
      });

      if (!fullEmployee) {
        this.logger.error(
          `Failed to fetch created employee: ${employee.userId}`
        );
        throw new Error('Employee created but could not be retrieved');
      }

      // Metrics
      this.trackMetrics('create_employee', 'success', Date.now() - startTime);

      return {
        success: true,
        message: 'Employee created successfully',
        employee: await this.mapToEmployeeInfo(fullEmployee),
      };
    } catch (error) {
      // Rollback transaction if it exists and hasn't been committed
      if (transaction) {
        try {
          await transaction.rollback();
        } catch (rollbackError) {
          this.logger.error('Failed to rollback transaction', rollbackError);
        }
      }

      this.logger.error(
        `Error creating employee: ${error.message}`,
        error.stack
      );
      this.trackMetrics('create_employee', 'error', Date.now() - startTime);

      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new Error('Failed to create employee');
    }
  }

  private async createRelatedEmployeeRecords(
    userId: bigint,
    request: CreateEmployeeRequest
  ): Promise<void> {
    // Create address records from nested structure
    const addressRecords = [];

    // Present address from nested structure or flat fields
    const presentAddress =
      request.presentAddress?.presentAddress || request.presentAddressFlat;
    if (presentAddress) {
      addressRecords.push({
        userId: Number(userId),
        addressType: 'present',
        addressLine: presentAddress,
        country:
          request.presentAddress?.presentCountry || request.presentCountry,
        state: request.presentAddress?.presentState || request.presentState,
        city: request.presentAddress?.presentCity || request.presentCity,
        postalCode:
          request.presentAddress?.presentPostalCode ||
          request.presentPostalCode,
      });
    }

    // Permanent address from nested structure or flat fields
    const permanentAddress =
      request.permanentAddress?.permanentAddress ||
      request.permanentAddressFlat;
    if (permanentAddress) {
      addressRecords.push({
        userId: Number(userId),
        addressType: 'permanent',
        addressLine: permanentAddress,
        country:
          request.permanentAddress?.permanentCountry ||
          request.permanentCountry,
        state:
          request.permanentAddress?.permanentState ||
          request.permanentStateFlat,
        city:
          request.permanentAddress?.permanentCity || request.permanentCityFlat,
        postalCode:
          request.permanentAddress?.permanentPostalCode ||
          request.permanentPostalCode,
      });
    }

    // Legacy addresses array
    if (request.addresses?.length) {
      const legacyAddresses = request.addresses.map((addr) => ({
        userId: Number(userId),
        addressType: addr.addressType,
        addressLine: addr.addressLine,
        country: addr.country,
        state: addr.state,
        city: addr.city,
        postalCode: addr.postalCode,
      }));
      addressRecords.push(...legacyAddresses);
    }

    if (addressRecords.length > 0) {
      await this.employeeAddressModel.bulkCreate(addressRecords);
    }

    // Create emergency contact records
    const emergencyContactRecords = [];

    // From nested structure or flat fields
    const emergencyContactName =
      request.emergencyContact?.emergencyContactName ||
      request.emergencyContactName;
    if (emergencyContactName) {
      emergencyContactRecords.push({
        userId: Number(userId),
        category: request.emergencyContact?.emergencyContactType || 'Primary',
        name: emergencyContactName,
        relationship:
          request.emergencyContact?.emergencyContactRelation ||
          request.emergencyContactRelation,
        address:
          request.emergencyContact?.emergencyContactAddress ||
          request.emergencyContactAddress,
        phoneNumber:
          request.emergencyContact?.emergencyContactPhone ||
          request.emergencyContactPhone,
        email:
          request.emergencyContact?.emergencyContactEmail ||
          request.emergencyContactEmail,
      });
    }

    // Legacy emergency contacts array
    if (request.emergencyContacts?.length) {
      const legacyContacts = request.emergencyContacts.map((contact) => ({
        userId: Number(userId),
        category: contact.category,
        name: contact.name,
        relationship: contact.relationship,
        address: contact.address,
        phoneNumber: contact.phoneNumber,
        email: contact.email,
      }));
      emergencyContactRecords.push(...legacyContacts);
    }

    if (emergencyContactRecords.length > 0) {
      await this.employeeEmergencyContactModel.bulkCreate(
        emergencyContactRecords
      );
    }

    // Create identity document records
    const identityDocRecords = [];

    // From nested structure
    if (request.identityInfo?.type) {
      identityDocRecords.push({
        userId: Number(userId),
        docType: request.identityInfo.type,
        number: request.identityInfo.number,
        nationality: request.identityInfo.country,
        issueDate: request.identityInfo.issueDate
          ? new Date(request.identityInfo.issueDate)
          : null,
        expiryDate: request.identityInfo.expiryDate
          ? new Date(request.identityInfo.expiryDate)
          : null,
      });
    }

    // Legacy identity docs array
    if (request.identityDocs?.length) {
      const legacyDocs = request.identityDocs.map((doc) => ({
        userId: Number(userId),
        docType: doc.docType,
        nationality: doc.nationality,
        number: doc.number,
        issueDate: doc.issueDate ? new Date(doc.issueDate) : null,
        expiryDate: doc.expiryDate ? new Date(doc.expiryDate) : null,
      }));
      identityDocRecords.push(...legacyDocs);
    }

    if (identityDocRecords.length > 0) {
      await this.employeeIdentityDocModel.bulkCreate(identityDocRecords);
    }

    // Create bank account records
    const bankAccountRecords = [];

    // From nested structure or flat fields
    const accountHolderName =
      request.bankAccount?.accountHolderName || request.accountHolderName;
    if (accountHolderName) {
      bankAccountRecords.push({
        userId: Number(userId),
        accountHolder: accountHolderName,
        accountNumber:
          request.bankAccount?.accountNumber || request.accountNumber,
        bankName: request.bankAccount?.bankName || request.bankName,
        branchName: request.bankAccount?.branchName || request.branchName,
      });
    }

    // Legacy bank accounts array
    if (request.bankAccounts?.length) {
      const legacyAccounts = request.bankAccounts.map((account) => ({
        userId: Number(userId),
        accountHolder: account.accountHolder,
        accountNumber: account.accountNumber,
        bankName: account.bankName,
        branchName: account.branchName,
      }));
      bankAccountRecords.push(...legacyAccounts);
    }

    if (bankAccountRecords.length > 0) {
      await this.employeeBankAccountModel.bulkCreate(bankAccountRecords);
    }
  }

  private async updateRelatedEmployeeRecords(
    userId: bigint,
    request: UpdateEmployeeRequest
  ): Promise<void> {
    // For updates, we'll replace existing records with new ones if data is provided

    // Update address records if address data is provided
    const hasAddressData =
      request.presentAddress?.presentAddress ||
      request.presentAddressFlat ||
      request.permanentAddress?.permanentAddress ||
      request.permanentAddressFlat ||
      request.addresses?.length;

    if (hasAddressData) {
      // Delete existing addresses
      await this.employeeAddressModel.destroy({
        where: { userId: Number(userId) },
      });

      // Create new address records
      const addressRecords = [];

      // Present address from nested structure or flat fields
      const presentAddress =
        request.presentAddress?.presentAddress || request.presentAddressFlat;
      if (presentAddress) {
        addressRecords.push({
          userId: Number(userId),
          addressType: 'present',
          addressLine: presentAddress,
          country:
            request.presentAddress?.presentCountry || request.presentCountry,
          state: request.presentAddress?.presentState || request.presentState,
          city: request.presentAddress?.presentCity || request.presentCity,
          postalCode:
            request.presentAddress?.presentPostalCode ||
            request.presentPostalCode,
        });
      }

      // Permanent address from nested structure or flat fields
      const permanentAddress =
        request.permanentAddress?.permanentAddress ||
        request.permanentAddressFlat;
      if (permanentAddress) {
        addressRecords.push({
          userId: Number(userId),
          addressType: 'permanent',
          addressLine: permanentAddress,
          country:
            request.permanentAddress?.permanentCountry ||
            request.permanentCountry,
          state:
            request.permanentAddress?.permanentState ||
            request.permanentStateFlat,
          city:
            request.permanentAddress?.permanentCity ||
            request.permanentCityFlat,
          postalCode:
            request.permanentAddress?.permanentPostalCode ||
            request.permanentPostalCode,
        });
      }

      // Legacy addresses array
      if (request.addresses?.length) {
        const legacyAddresses = request.addresses.map((addr) => ({
          userId: Number(userId),
          addressType: addr.addressType,
          addressLine: addr.addressLine,
          country: addr.country,
          state: addr.state,
          city: addr.city,
          postalCode: addr.postalCode,
        }));
        addressRecords.push(...legacyAddresses);
      }

      if (addressRecords.length > 0) {
        await this.employeeAddressModel.bulkCreate(addressRecords);
      }
    }

    // Update emergency contact records if contact data is provided
    const hasEmergencyContactData =
      request.emergencyContact?.emergencyContactName ||
      request.emergencyContactName ||
      request.emergencyContacts?.length;

    if (hasEmergencyContactData) {
      // Delete existing emergency contacts
      await this.employeeEmergencyContactModel.destroy({
        where: { userId: Number(userId) },
      });

      // Create new emergency contact records
      const emergencyContactRecords = [];

      // From nested structure or flat fields
      const emergencyContactName =
        request.emergencyContact?.emergencyContactName ||
        request.emergencyContactName;
      if (emergencyContactName) {
        emergencyContactRecords.push({
          userId: Number(userId),
          category: request.emergencyContact?.emergencyContactType || 'Primary',
          name: emergencyContactName,
          relationship:
            request.emergencyContact?.emergencyContactRelation ||
            request.emergencyContactRelation,
          address:
            request.emergencyContact?.emergencyContactAddress ||
            request.emergencyContactAddress,
          phoneNumber:
            request.emergencyContact?.emergencyContactPhone ||
            request.emergencyContactPhone,
          email:
            request.emergencyContact?.emergencyContactEmail ||
            request.emergencyContactEmail,
        });
      }

      // Legacy emergency contacts array
      if (request.emergencyContacts?.length) {
        const legacyContacts = request.emergencyContacts.map((contact) => ({
          userId: Number(userId),
          category: contact.category,
          name: contact.name,
          relationship: contact.relationship,
          address: contact.address,
          phoneNumber: contact.phoneNumber,
          email: contact.email,
        }));
        emergencyContactRecords.push(...legacyContacts);
      }

      if (emergencyContactRecords.length > 0) {
        await this.employeeEmergencyContactModel.bulkCreate(
          emergencyContactRecords
        );
      }
    }

    // Update identity document records if identity data is provided
    const hasIdentityData =
      request.identityInfo?.type || request.identityDocs?.length;

    if (hasIdentityData) {
      // Delete existing identity docs
      await this.employeeIdentityDocModel.destroy({
        where: { userId: Number(userId) },
      });

      // Create new identity document records
      const identityDocRecords = [];

      // From nested structure
      if (request.identityInfo?.type) {
        identityDocRecords.push({
          userId: Number(userId),
          docType: request.identityInfo.type,
          nationality: request.identityInfo.country,
          issueDate: request.identityInfo.issueDate
            ? new Date(request.identityInfo.issueDate)
            : null,
          expiryDate: request.identityInfo.expiryDate
            ? new Date(request.identityInfo.expiryDate)
            : null,
        });
      }

      // Legacy identity docs array
      if (request.identityDocs?.length) {
        const legacyDocs = request.identityDocs.map((doc) => ({
          userId: Number(userId),
          docType: doc.docType,
          nationality: doc.nationality,
          issueDate: doc.issueDate ? new Date(doc.issueDate) : null,
          expiryDate: doc.expiryDate ? new Date(doc.expiryDate) : null,
        }));
        identityDocRecords.push(...legacyDocs);
      }

      if (identityDocRecords.length > 0) {
        await this.employeeIdentityDocModel.bulkCreate(identityDocRecords);
      }
    }

    // Update bank account records if bank data is provided
    const hasBankAccountData =
      request.bankAccount?.accountHolderName ||
      request.accountHolderName ||
      request.bankAccounts?.length;

    if (hasBankAccountData) {
      // Delete existing bank accounts
      await this.employeeBankAccountModel.destroy({
        where: { userId: Number(userId) },
      });

      // Create new bank account records
      const bankAccountRecords = [];

      // From nested structure or flat fields
      const accountHolderName =
        request.bankAccount?.accountHolderName || request.accountHolderName;
      if (accountHolderName) {
        bankAccountRecords.push({
          userId: Number(userId),
          accountHolder: accountHolderName,
          accountNumber:
            request.bankAccount?.accountNumber || request.accountNumber,
          bankName: request.bankAccount?.bankName || request.bankName,
          branchName: request.bankAccount?.branchName || request.branchName,
        });
      }

      // Legacy bank accounts array
      if (request.bankAccounts?.length) {
        const legacyAccounts = request.bankAccounts.map((account) => ({
          userId: Number(userId),
          accountHolder: account.accountHolder,
          accountNumber: account.accountNumber,
          bankName: account.bankName,
          branchName: account.branchName,
        }));
        bankAccountRecords.push(...legacyAccounts);
      }

      if (bankAccountRecords.length > 0) {
        await this.employeeBankAccountModel.bulkCreate(bankAccountRecords);
      }
    }
  }

  async mapToEmployeeInfo(employee: EmployeePersonal): Promise<EmployeeInfo> {
    // If employee already has relations loaded, use it directly
    let fullEmployee = employee;

    // If relations are not loaded, fetch them
    if (
      !employee.addresses &&
      !employee.emergencyContacts &&
      !employee.identityDocs &&
      !employee.bankAccounts
    ) {
      const fetchedEmployee = await this.employeeModel.findByPk(
        employee.userId,
        {
          include: { all: true, nested: true },
        }
      );
      if (!fetchedEmployee) {
        throw new NotFoundException(
          `Employee not found with userId: ${employee.userId}`
        );
      }
      fullEmployee = fetchedEmployee;
    }
    return {
      userId: BigInt(fullEmployee.userId),

      // Personal Information
      lastName: fullEmployee.lastName,
      firstName: fullEmployee.firstName,
      phone: fullEmployee.phone,
      dateOfBirth: fullEmployee.dateOfBirth?.toISOString(),
      bloodGroup: fullEmployee.bloodGroup,
      gender: fullEmployee.gender,
      nationality: fullEmployee.nationality,
      maritalStatus: fullEmployee.maritalStatus,

      // Employment Information
      joiningDate: fullEmployee.joiningDate?.toISOString(),
      jobType: fullEmployee.jobType,
      jobStatus: fullEmployee.jobStatus,

      // Department Information
      employeeId: fullEmployee.employeeId,
      department: fullEmployee.department,
      designation: fullEmployee.designation,
      supervisor: fullEmployee.supervisor,
      workLocation: fullEmployee.workLocation,

      // Address Information
      presentAddress: fullEmployee.presentAddress,
      presentCountry: fullEmployee.presentCountry,
      presentState: fullEmployee.presentState,
      presentCity: fullEmployee.presentCity,
      presentPostalCode: fullEmployee.presentPostalCode,
      permanentAddress: fullEmployee.permanentAddress,
      permanentCountry: fullEmployee.permanentCountry,
      permanentState: fullEmployee.permanentState,
      permanentCity: fullEmployee.permanentCity,
      permanentPostalCode: fullEmployee.permanentPostalCode,

      // Emergency Contact
      emergencyContactType: fullEmployee.emergencyContactType,
      emergencyContactName: fullEmployee.emergencyContactName,
      emergencyContactRelation: fullEmployee.emergencyContactRelation,
      emergencyContactPhone: fullEmployee.emergencyContactPhone,
      emergencyContactEmail: fullEmployee.emergencyContactEmail,
      emergencyContactAddress: fullEmployee.emergencyContactAddress,

      // Identity Information
      identityType: fullEmployee.identityType,
      identityCountry: fullEmployee.identityCountry,
      identityNumber: fullEmployee.identityNumber,
      identityIssueDate: fullEmployee.identityIssueDate,
      identityExpiryDate: fullEmployee.identityExpiryDate,

      // Bank Account Information
      accountHolderName: fullEmployee.accountHolderName,
      accountNumber: fullEmployee.accountNumber,
      bankName: fullEmployee.bankName,
      branchName: fullEmployee.branchName,
      routingNumber: fullEmployee.routingNumber,
      accountType: fullEmployee.accountType,

      // Social Profile
      linkedinUrl: fullEmployee.linkedinUrl,
      twitterUrl: fullEmployee.twitterUrl,
      facebookUrl: fullEmployee.facebookUrl,
      instagramUrl: fullEmployee.instagramUrl,

      // Legacy fields
      agencyId: fullEmployee.agencyId
        ? BigInt(fullEmployee.agencyId)
        : undefined,
      orgId: fullEmployee.orgId,
      addresses: fullEmployee.addresses?.map((addr) => ({
        id: BigInt(addr.id),
        userId: BigInt(addr.userId),
        addressType: addr.addressType,
        addressLine: addr.addressLine,
        country: addr.country,
        state: addr.state,
        city: addr.city,
        postalCode: addr.postalCode,
      })),
      emergencyContacts: fullEmployee.emergencyContacts?.map((contact) => ({
        id: BigInt(contact.id),
        userId: BigInt(contact.userId),
        category: contact.category,
        name: contact.name,
        relationship: contact.relationship,
        address: contact.address,
        phoneNumber: contact.phoneNumber,
        email: contact.email,
      })),
      identityDocs: fullEmployee.identityDocs?.map((doc) => ({
        id: BigInt(doc.id),
        userId: BigInt(doc.userId),
        docType: doc.docType,
        nationality: doc.nationality,
        issueDate: doc.issueDate?.toISOString(),
        expiryDate: doc.expiryDate?.toISOString(),
      })),
      bankAccounts: fullEmployee.bankAccounts?.map((account) => ({
        id: BigInt(account.id),
        userId: BigInt(account.userId),
        accountHolder: account.accountHolder,
        accountNumber: account.accountNumber,
        bankName: account.bankName,
        branchName: account.branchName,
      })),

      createdAt: fullEmployee.createdAt,
      updatedAt: fullEmployee.updatedAt,
    };
  }

  // New updateEmployee method that properly handles the UpdateEmployeeRequest
  async updateEmployee(
    request: UpdateEmployeeRequest
  ): Promise<EmployeeResponse> {
    const startTime = Date.now();
    let transaction;

    try {
      this.logger.log(`Updating employee: ${request.id}`);

      // Start transaction
      transaction = await this.userModel.sequelize.transaction();

      // 1. Find the employee
      const employee = await this.employeeModel.findByPk(Number(request.id), {
        include: { all: true, nested: true },
        transaction,
      });

      if (!employee) {
        await transaction.rollback();
        return {
          success: false,
          message: 'Employee not found',
        };
      }

      // 2. Validate job type and status if provided
      if (request.jobType && !JOB_TYPES.includes(request.jobType as any)) {
        await transaction.rollback();
        throw new BadRequestException(
          `Invalid job type. Must be one of: ${JOB_TYPES.join(', ')}`
        );
      }
      if (
        request.jobStatus &&
        !JOB_STATUSES.includes(request.jobStatus as any)
      ) {
        await transaction.rollback();
        throw new BadRequestException(
          `Invalid job status. Must be one of: ${JOB_STATUSES.join(', ')}`
        );
      }

      // 3. Prepare update data with comprehensive mapping
      const updateData: Partial<UpdateEmployeeData> = {};

      // Personal Information
      if (request.firstName !== undefined)
        updateData.firstName = request.firstName;
      if (request.lastName !== undefined)
        updateData.lastName = request.lastName;
      if (request.email !== undefined) updateData.email = request.email;
      if (request.phone !== undefined) updateData.phone = request.phone;
      if (request.bloodGroup !== undefined)
        updateData.bloodGroup = request.bloodGroup;
      if (request.gender !== undefined) updateData.gender = request.gender;
      if (request.nationality !== undefined)
        updateData.nationality = request.nationality;
      if (request.maritalStatus !== undefined)
        updateData.maritalStatus = request.maritalStatus;

      // Employment Information
      if (request.jobType !== undefined) updateData.jobType = request.jobType;
      if (request.jobStatus !== undefined)
        updateData.jobStatus = request.jobStatus;

      // Department Information - prioritize nested structure
      if (
        request.departmentInfo?.employeeId !== undefined ||
        request.employeeId !== undefined
      )
        updateData.employeeId =
          request.departmentInfo?.employeeId || request.employeeId;
      if (
        request.departmentInfo?.department !== undefined ||
        request.department !== undefined
      )
        updateData.department =
          request.departmentInfo?.department || request.department;
      if (
        request.departmentInfo?.designation !== undefined ||
        request.designation !== undefined
      )
        updateData.designation =
          request.departmentInfo?.designation || request.designation;
      if (request.departmentInfo?.supervisor !== undefined)
        updateData.supervisor = request.departmentInfo.supervisor;
      if (
        request.departmentInfo?.workLocation !== undefined ||
        request.workLocation !== undefined
      )
        updateData.workLocation =
          request.departmentInfo?.workLocation || request.workLocation;

      // Address Information - prioritize nested structure
      if (
        request.presentAddress?.presentAddress !== undefined ||
        request.presentAddressFlat !== undefined
      )
        updateData.presentAddress =
          request.presentAddress?.presentAddress || request.presentAddressFlat;
      if (
        request.presentAddress?.presentCountry !== undefined ||
        request.presentCountry !== undefined
      )
        updateData.presentCountry =
          request.presentAddress?.presentCountry || request.presentCountry;
      if (
        request.presentAddress?.presentState !== undefined ||
        request.presentState !== undefined
      )
        updateData.presentState =
          request.presentAddress?.presentState || request.presentState;
      if (
        request.presentAddress?.presentCity !== undefined ||
        request.presentCity !== undefined
      )
        updateData.presentCity =
          request.presentAddress?.presentCity || request.presentCity;
      if (
        request.presentAddress?.presentPostalCode !== undefined ||
        request.presentPostalCode !== undefined
      )
        updateData.presentPostalCode =
          request.presentAddress?.presentPostalCode ||
          request.presentPostalCode;

      if (
        request.permanentAddress?.permanentAddress !== undefined ||
        request.permanentAddressFlat !== undefined
      )
        updateData.permanentAddress =
          request.permanentAddress?.permanentAddress ||
          request.permanentAddressFlat;
      if (
        request.permanentAddress?.permanentCountry !== undefined ||
        request.permanentCountry !== undefined
      )
        updateData.permanentCountry =
          request.permanentAddress?.permanentCountry ||
          request.permanentCountry;
      if (
        request.permanentAddress?.permanentState !== undefined ||
        request.permanentStateFlat !== undefined
      )
        updateData.permanentState =
          request.permanentAddress?.permanentState ||
          request.permanentStateFlat;
      if (
        request.permanentAddress?.permanentCity !== undefined ||
        request.permanentCityFlat !== undefined
      )
        updateData.permanentCity =
          request.permanentAddress?.permanentCity || request.permanentCityFlat;
      if (
        request.permanentAddress?.permanentPostalCode !== undefined ||
        request.permanentPostalCode !== undefined
      )
        updateData.permanentPostalCode =
          request.permanentAddress?.permanentPostalCode ||
          request.permanentPostalCode;

      // Emergency Contact - prioritize nested structure
      if (request.emergencyContact?.emergencyContactType !== undefined)
        updateData.emergencyContactType =
          request.emergencyContact.emergencyContactType;
      if (
        request.emergencyContact?.emergencyContactName !== undefined ||
        request.emergencyContactName !== undefined
      )
        updateData.emergencyContactName =
          request.emergencyContact?.emergencyContactName ||
          request.emergencyContactName;
      if (
        request.emergencyContact?.emergencyContactRelation !== undefined ||
        request.emergencyContactRelation !== undefined
      )
        updateData.emergencyContactRelation =
          request.emergencyContact?.emergencyContactRelation ||
          request.emergencyContactRelation;
      if (
        request.emergencyContact?.emergencyContactPhone !== undefined ||
        request.emergencyContactPhone !== undefined
      )
        updateData.emergencyContactPhone =
          request.emergencyContact?.emergencyContactPhone ||
          request.emergencyContactPhone;
      if (
        request.emergencyContact?.emergencyContactEmail !== undefined ||
        request.emergencyContactEmail !== undefined
      )
        updateData.emergencyContactEmail =
          request.emergencyContact?.emergencyContactEmail ||
          request.emergencyContactEmail;
      if (
        request.emergencyContact?.emergencyContactAddress !== undefined ||
        request.emergencyContactAddress !== undefined
      )
        updateData.emergencyContactAddress =
          request.emergencyContact?.emergencyContactAddress ||
          request.emergencyContactAddress;

      // Identity Information - prioritize nested structure
      if (request.identityInfo?.type !== undefined)
        updateData.identityType = request.identityInfo.type;
      if (request.identityInfo?.country !== undefined)
        updateData.identityCountry = request.identityInfo.country;
      if (request.identityInfo?.number !== undefined)
        updateData.identityNumber = request.identityInfo.number;
      if (request.identityInfo?.issueDate !== undefined)
        updateData.identityIssueDate = request.identityInfo.issueDate;
      if (request.identityInfo?.expiryDate !== undefined)
        updateData.identityExpiryDate = request.identityInfo.expiryDate;

      // Bank Account Information - prioritize nested structure
      if (
        request.bankAccount?.accountHolderName !== undefined ||
        request.accountHolderName !== undefined
      )
        updateData.accountHolderName =
          request.bankAccount?.accountHolderName || request.accountHolderName;
      if (
        request.bankAccount?.accountNumber !== undefined ||
        request.accountNumber !== undefined
      )
        updateData.accountNumber =
          request.bankAccount?.accountNumber || request.accountNumber;
      if (
        request.bankAccount?.bankName !== undefined ||
        request.bankName !== undefined
      )
        updateData.bankName = request.bankAccount?.bankName || request.bankName;
      if (
        request.bankAccount?.branchName !== undefined ||
        request.branchName !== undefined
      )
        updateData.branchName =
          request.bankAccount?.branchName || request.branchName;
      if (
        request.bankAccount?.routingNumber !== undefined ||
        request.routingNumber !== undefined
      )
        updateData.routingNumber =
          request.bankAccount?.routingNumber || request.routingNumber;
      if (
        request.bankAccount?.accountType !== undefined ||
        request.accountType !== undefined
      )
        updateData.accountType =
          request.bankAccount?.accountType || request.accountType;

      // Social Profile - prioritize nested structure
      if (
        request.socialLinks?.linkedinUrl !== undefined ||
        request.linkedinUrl !== undefined
      )
        updateData.linkedinUrl =
          request.socialLinks?.linkedinUrl || request.linkedinUrl;
      if (
        request.socialLinks?.twitterUrl !== undefined ||
        request.twitterUrl !== undefined
      )
        updateData.twitterUrl =
          request.socialLinks?.twitterUrl || request.twitterUrl;
      if (
        request.socialLinks?.facebookUrl !== undefined ||
        request.facebookUrl !== undefined
      )
        updateData.facebookUrl =
          request.socialLinks?.facebookUrl || request.facebookUrl;
      if (
        request.socialLinks?.instagramUrl !== undefined ||
        request.instagramUrl !== undefined
      )
        updateData.instagramUrl =
          request.socialLinks?.instagramUrl || request.instagramUrl;

      // Legacy fields
      if (request.agencyId !== undefined)
        updateData.agencyId = request.agencyId;
      if (request.orgId !== undefined) updateData.orgId = request.orgId;

      // Handle date fields with proper validation
      if (request.joiningDate !== undefined) {
        if (request.joiningDate) {
          const joiningDate = new Date(request.joiningDate);
          if (isNaN(joiningDate.getTime())) {
            await transaction.rollback();
            throw new BadRequestException('Invalid joiningDate format');
          }
          updateData.joiningDate = joiningDate;
        } else {
          updateData.joiningDate = null;
        }
      }

      if (request.dateOfBirth !== undefined) {
        if (request.dateOfBirth) {
          const dateOfBirth = new Date(request.dateOfBirth);
          if (isNaN(dateOfBirth.getTime())) {
            await transaction.rollback();
            throw new BadRequestException('Invalid dateOfBirth format');
          }
          updateData.dateOfBirth = dateOfBirth;
        } else {
          updateData.dateOfBirth = null;
        }
      }

      // 4. Update the employee record
      await employee.update(updateData as any, { transaction });

      await transaction.commit();

      // 5. Handle related data updates (addresses, contacts, etc.) if provided
      await this.updateRelatedEmployeeRecords(BigInt(employee.userId), request);

      // 6. Audit log
      await this.createAuditLog({
        userId: Number(request.requestUserId),
        userRole: request.roleName,
        actions: 'UPDATE_EMPLOYEE',
        serviceName: 'auth-service',
        resourceType: 'Employee',
        resourceId: Number(employee.userId),
        description: `Updated employee: ${employee.userId}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });

      this.logger.log(`Employee updated successfully: ${employee.userId}`);

      // 7. Fetch the updated employee with all relations
      const updatedEmployee = await this.employeeModel.findByPk(
        employee.userId,
        {
          include: { all: true, nested: true },
        }
      );

      // Metrics
      this.trackMetrics('update_employee', 'success', Date.now() - startTime);

      return {
        success: true,
        message: 'Employee updated successfully',
        employee: await this.mapToEmployeeInfo(updatedEmployee),
      };
    } catch (error) {
      // Rollback transaction if it exists and hasn't been committed
      if (transaction) {
        try {
          await transaction.rollback();
        } catch (rollbackError) {
          this.logger.error('Failed to rollback transaction', rollbackError);
        }
      }

      this.logger.error(
        `Error updating employee: ${error.message}`,
        error.stack
      );

      // Metrics
      this.trackMetrics('update_employee', 'error', Date.now() - startTime);

      return {
        success: false,
        message: error.message || 'Failed to update employee',
      };
    }
  }

  // Legacy methods remain unchanged
  async create(data: CreateEmployeeDto): Promise<EmployeePersonal> {
    return this.employeeModel.create(data as any);
  }

  async findAll(): Promise<EmployeePersonal[]> {
    return this.employeeModel.findAll({
      include: { all: true, nested: true },
    });
  }

  async findOne(id: number): Promise<EmployeePersonal> {
    const emp = await this.employeeModel.findByPk(id, {
      include: { all: true, nested: true },
    });
    if (!emp) throw new NotFoundException(`Employee ${id} not found`);
    return emp;
  }

  async update(id: number, data: UpdateEmployeeDto): Promise<EmployeePersonal> {
    const emp = await this.findOne(id);
    await emp.update(data as any);
    return emp;
  }

  async remove(id: number): Promise<{ deleted: boolean }> {
    const emp = await this.findOne(id);
    await emp.destroy();
    return { deleted: true };
  }
}
