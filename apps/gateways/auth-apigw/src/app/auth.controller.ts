import {
  Body,
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Headers,
  UnauthorizedException,
  Param,
  Query,
  HttpException,
  HttpStatus,
  Logger,
  Req,
  Ip,
} from '@nestjs/common';
import { AuthClientService } from './auth.service';
import { firstValueFrom } from 'rxjs';
import {
  AssignDepartmentRequest,
  BulkCreateModulesResponse,
  CreateDepartmentRequest,
  CreateModuleRequest,
  CreateModuleResponse,
  CreateRoleWithDetailsRequest,
  CreateOrganizationRoleRequest,
  BulkCreateDepartmentRequest,
  ModuleInput,
  NewDepartmentInfo,
  CreateEmployeeRequest,
  UpdateEmployeeRequest,
  CreateRoleRequest,
  UpdateRoleRequest,
  UpdateOrgDepartmentRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  VerifyResetTokenRequest,
} from './auth.interface';
import { Permissions, Public, CurrentUser } from '@apply-goal-backend/auth';
import { toNumber } from './utils/serializeResponse';
import { UserPermissions } from '@apply-goal-backend/common';

@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(private readonly authService: AuthClientService) {}

  // ---------- Authentication --------------
  // #region
  @Public()
  @Post('register')
  async register(
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body()
    registerData: {
      name: string;
      email: string;
      nationality: string;
      organizationName: string;
      password: string;
      phone: string;
      roleName: string;
      departmentName: string;
    }
  ) {
    try {
      this.logger.log(
        `Registration attempt for user: ${registerData.roleName}`
      );
      return await firstValueFrom(
        this.authService.register({
          ...registerData,
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error(
        `Registration failed for user: ${registerData.email}`,
        error.stack
      );
      throw new HttpException('Registration failed', HttpStatus.BAD_REQUEST);
    }
  }

  @Public()
  @Post('login')
  async login(
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body()
    loginData: {
      email: string;
      password: string;
    }
  ) {
    try {
      this.logger.log(`Login attempt for user: ${loginData.email}`);
      return await this.authService.login(
        loginData.email,
        loginData.password,
        ipAddress,
        userAgent
      );
    } catch (error) {
      this.logger.error(
        `Login failed for user: ${loginData.email}`,
        error.stack
      );
      throw new UnauthorizedException('Invalid credentials');
    }
  }

  @Public()
  @Post('logout')
  async logout(
    @Headers('authorization') authHeader: string,
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedException('Invalid token format');
    }

    const token = authHeader.split(' ')[1];
    try {
      this.logger.log('Logout request received');
      return await firstValueFrom(this.authService.logout(token));
    } catch (error) {
      this.logger.error('Logout failed', error.stack);
      throw new HttpException(
        'Logout failed',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Public()
  @Get('validate')
  async validateToken(
    @Headers('authorization') authHeader: string,
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedException('Invalid token format');
    }

    const token = authHeader.split(' ')[1];
    try {
      this.logger.log('Token validation request received');
      return await this.authService.validateToken(token);
    } catch (error) {
      this.logger.error('Token validation failed', error.stack);
      throw new UnauthorizedException('Invalid token');
    }
  }

  @Public()
  @Post('refresh')
  async refreshToken(
    @Body() refreshData: { refreshToken: string },
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log('Token refresh request received');
      return await firstValueFrom(
        this.authService.refreshToken(refreshData.refreshToken)
      );
    } catch (error) {
      this.logger.error('Token refresh failed', error.stack);
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  @Public()
  @Post('sso')
  async ssoLogin(
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body()
    ssoData: {
      provider: 'google'; // extend for others later
      token: string;
      email: string;
      name: string;
    }
  ) {
    try {
      this.logger.log(`SSO login attempt using ${ssoData.provider} provider`);
      const response = this.authService.ssoAuth({
        provider: ssoData.provider,
        token: ssoData.token,
        email: ssoData.email,
        name: ssoData.name,
        ipAddress,
        userAgent,
      });
      return response;
    } catch (error) {
      this.logger.error(
        `SSO login failed for provider ${ssoData.provider}`,
        error.stack
      );
      throw new HttpException(
        'SSO Authentication failed',
        HttpStatus.UNAUTHORIZED
      );
    }
  }

  @Public()
  @Post('verify-otp')
  async verifyOTP(
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body()
    otpData: {
      email: string;
      otp: string;
      type: string;
    }
  ) {
    try {
      this.logger.log(`OTP validation attempt for user: ${otpData.email}`);
      return await firstValueFrom(
        this.authService.verifyOtp(
          otpData.email,
          otpData.otp,
          otpData.type,
          ipAddress,
          userAgent
        )
      );
    } catch (error) {
      this.logger.error(
        `OTP validation failed for user: ${otpData.email}`,
        error.stack
      );
      throw new HttpException('OTP validation failed', HttpStatus.BAD_REQUEST);
    }
  }

  @Public()
  @Post('generate-otp')
  async generateOTP(
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body()
    otpData: {
      email: string;
      type: string;
    }
  ) {
    try {
      this.logger.log(`OTP generation request for user: ${otpData.email}`);
      return await firstValueFrom(
        this.authService.generateOtp(
          otpData.email,
          otpData.type,
          ipAddress,
          userAgent
        )
      );
    } catch (error) {
      this.logger.error(
        `OTP generation failed for user: ${otpData.email}`,
        error.stack
      );
      throw new HttpException('OTP generation failed', HttpStatus.BAD_REQUEST);
    }
  }
  // #endregion

  // ---------- User management endpoints ------------------
  // #region

  // #endregion

  // ----------- Role ----------
  // #region
  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Post('role')
  async createRoleWithDetails(
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() roleData: CreateRoleWithDetailsRequest
  ) {
    try {
      return await firstValueFrom(
        this.authService.createRoleWithDetails({
          ...roleData,
          getArgs: {
            userId,
            roleName: roles[0],
            ipAddress,
            userAgent,
          },
        })
      );
    } catch (err) {
      this.logger.error('createRoleWithDetails failed', err.stack);
      throw new HttpException(
        'Failed to create role',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Get('role-details/:roleName')
  async getRoleDetails(
    @Param('roleName') roleName: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      return await firstValueFrom(
        this.authService.getRoleDetails(roleName, {
          userId,
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
    } catch (err) {
      this.logger.error('getRoleDetails failed', err.stack);
      throw new HttpException(
        'Failed to get role details',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Get('roles')
  async getRolesWithDetails(
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log('Get roles and permissions with users request');
      return await firstValueFrom(
        this.authService.getRolesWithDetails({
          userId,
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error(
        'Get roles and permissions with users failed',
        error.stack
      );
      throw new HttpException(
        'Failed to get roles and permissions with users',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Put('role/:id/:name')
  async renameRole(
    @Param('id') id: number,
    @Param('name') name: string,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log(`Update role request: ${name}`);
      return await firstValueFrom(
        this.authService.renameRole({
          id,
          roleName: name,
          getArgs: {
            userId,
            roleName: roles[0],
            ipAddress,
            userAgent,
          },
        })
      );
    } catch (error) {
      this.logger.error(`Update role failed for: ${name}`, error.stack);
      throw new HttpException('Failed to update role', HttpStatus.BAD_REQUEST);
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Delete('role/:id')
  async deleteRole(
    @Param('id') id: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log(`Delete role request: ${id}`);
      return await firstValueFrom(
        this.authService.deleteRole({
          id,
          getArgs: {
            userId,
            roleName: roles[0],
            ipAddress,
            userAgent,
          },
        })
      );
    } catch (error) {
      this.logger.error(`Delete role failed for: ${id}`, error.stack);
      throw new HttpException('Failed to delete role', HttpStatus.BAD_REQUEST);
    }
  }

  // #endregion

  // ─── Module management endpoints ────────────────────────────────────────
  // #region
  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Post('modules')
  async createModule(
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() moduleData: CreateModuleRequest
  ): Promise<CreateModuleResponse> {
    try {
      this.logger.log(`Create module request: ${moduleData.name}`);
      return await firstValueFrom(this.authService.createModule(moduleData));
    } catch (error) {
      this.logger.error(
        `Create module failed for: ${moduleData.name}`,
        error.stack
      );
      throw new HttpException(
        'Failed to create module',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Post('modules/bulk')
  async bulkCreateModules(
    @CurrentUser('sub') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() modulesData: ModuleInput[]
  ): Promise<BulkCreateModulesResponse> {
    try {
      return await firstValueFrom(
        this.authService.bulkCreateModules({
          modules: modulesData,
          userId,
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
    } catch (err) {
      this.logger.error('bulkCreateModules failed', err.stack);
      throw new HttpException(
        'Failed to create modules',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Get('modules')
  async listModules(
    @CurrentUser('sub') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log('List modules request');
      const resp = await firstValueFrom(
        this.authService.listModules({
          userId,
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
      resp.modules = resp.modules.map((m: any) => ({
        ...m,
        id: toNumber(m.id),
        features: m.features?.map((f: any) => ({
          ...f,
          id: toNumber(f.id),
          subFeatures: f.subFeatures?.map((sf: any) => ({
            ...sf,
            id: toNumber(sf.id),
          })),
        })),
      }));
      return resp;
    } catch (error) {
      this.logger.error('List modules failed', error.stack);
      throw new HttpException(
        'Failed to list modules',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
  // #endregion

  // ─── Department management endpoints ───────────────────────────────────
  // #region
  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Post('departments')
  async createDepartment(
    @CurrentUser('id') userId: string,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() deptData: NewDepartmentInfo[],
    @Req() req: Request
  ) {
    try {
      this.logger.log(
        `Create department request: ${JSON.stringify(deptData, null, 2)}`
      );
      return await firstValueFrom(
        this.authService.createDepartment({
          departments: deptData,
          userId: Number(userId),
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error(
        `Create department failed for: ${JSON.stringify(deptData, null, 2)}`,
        error.stack
      );
      throw new HttpException(
        'Failed to create department',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Post('departments/assign')
  async assignDepartment(
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() assignData: AssignDepartmentRequest
  ) {
    try {
      this.logger.log(
        `Assign department ${assignData.departmentId} to user ${assignData.userId}`
      );
      return await firstValueFrom(
        this.authService.assignDepartment(assignData)
      );
    } catch (error) {
      this.logger.error(
        `Failed to assign department ${assignData.departmentId} to user ${assignData.userId}`,
        error.stack
      );
      throw new HttpException(
        'Failed to assign department',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Get('departments')
  async listDepartments(
    @CurrentUser('id') userId: string,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Query('includeHierarchy') includeHierarchy?: boolean,
    @Query('organizationId') organizationId?: number
  ) {
    try {
      this.logger.log('List departments request with hierarchy support');
      return await firstValueFrom(
        this.authService.listDepartments({
          userId: Number(userId),
          roleName: roles[0],
          ipAddress,
          userAgent,
          includeHierarchy: includeHierarchy === true,
          organizationId: organizationId ? Number(organizationId) : undefined,
        })
      );
    } catch (error) {
      this.logger.error('List departments failed', error.stack);
      throw new HttpException(
        'Failed to list departments',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Get('departments-with-users')
  async listDepartmentsWithUsers(
    @CurrentUser('id') userId: string,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log('List departments with users request');
      return await firstValueFrom(
        this.authService.listDepartmentsWithUsers({
          userId: Number(userId),
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error('List departments with users failed', error.stack);
      throw new HttpException(
        'Failed to list departments with users',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
  // #endregion

  // ─── Employee Management endpoints ─────────────────────────────────────
  // #region
  @Permissions(UserPermissions.SS_EMPLOYEE_MANAGEMENT)
  @Post('employees')
  async createEmployee(
    @CurrentUser('id') userId: number,
    @CurrentUser('organizationId') organizationId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() employeeData: CreateEmployeeRequest
  ) {
    try {
      this.logger.log(
        `Create employee request for email: ${employeeData.email}`
      );

      // Validate required fields
      if (!employeeData.email) {
        throw new HttpException(
          'Email is required for employee creation',
          HttpStatus.BAD_REQUEST
        );
      }

      // Validate organization access
      if (!organizationId) {
        throw new HttpException(
          'Organization ID not found in user token',
          HttpStatus.UNAUTHORIZED
        );
      }

      // Call the service directly with the structured data
      const result = await this.authService.createEmployee({
        ...employeeData,
        organizationId,
        requestUserId: userId,
        roleName: roles[0] || 'Unknown',
        ipAddress,
        userAgent,
      });
      this.logger.log(`Employee created successfully`);
      return result;
    } catch (error) {
      this.logger.error(
        `Create employee failed for email: ${employeeData.email}`,
        error.stack
      );

      // Handle specific error cases
      if (error.message?.includes('Email already exists')) {
        throw new HttpException('Email already exists', HttpStatus.CONFLICT);
      }

      if (
        error.message?.includes('Role') &&
        error.message?.includes('not found')
      ) {
        throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
      }

      throw new HttpException(
        'Failed to create employee',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_EMPLOYEE_MANAGEMENT)
  @Get('employees/:id')
  async getEmployee(
    @Param('id') id: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('organizationId') organizationId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log(`Get employee request: ${id}`);
      return await firstValueFrom(
        this.authService.getEmployee({
          id,
          organizationId,
          requestUserId: userId,
          roleName: roles[0] || 'Unknown',
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error(`Get employee failed for: ${id}`, error.stack);
      throw new HttpException('Failed to get employee', HttpStatus.BAD_REQUEST);
    }
  }

  @Permissions(UserPermissions.SS_EMPLOYEE_MANAGEMENT)
  @Get('employees')
  async listEmployees(
    @CurrentUser('id') userId: number,
    @CurrentUser('organizationId') organizationId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Query('jobType') jobType?: string,
    @Query('jobStatus') jobStatus?: string
  ) {
    try {
      this.logger.log('List employees request');
      return await firstValueFrom(
        this.authService.listEmployees({
          page,
          limit,
          search,
          jobType,
          jobStatus,
          organizationId,
          requestUserId: userId,
          roleName: roles[0] || 'Unknown',
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error('List employees failed', error.stack);
      throw new HttpException(
        'Failed to list employees',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Permissions(UserPermissions.SS_EMPLOYEE_MANAGEMENT)
  @Put('employees/:id')
  async updateEmployee(
    @Param('id') id: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('organizationId') organizationId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() employeeData: UpdateEmployeeRequest
  ) {
    try {
      this.logger.log(`Update employee request: ${id}`);

      // Call the service directly with the structured data
      return await firstValueFrom(
        this.authService.updateEmployee({
          ...employeeData,
          id,
          organizationId,
          requestUserId: userId,
          roleName: roles[0] || 'Unknown',
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error(`Update employee failed for: ${id}`, error.stack);
      throw new HttpException(
        'Failed to update employee',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_EMPLOYEE_MANAGEMENT)
  @Delete('employees/:id')
  async removeEmployee(
    @Param('id') id: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('organizationId') organizationId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log(`Remove employee request: ${id}`);
      return await firstValueFrom(
        this.authService.removeEmployee({
          id,
          organizationId,
          requestUserId: userId,
          roleName: roles[0] || 'Unknown',
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error(`Remove employee failed for: ${id}`, error.stack);
      throw new HttpException(
        'Failed to remove employee',
        HttpStatus.BAD_REQUEST
      );
    }
  }
  // #endregion

  // ─── Organization-specific Role Management endpoints ─────────────────────────
  // #region
  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Post('organizations/:orgId/roles')
  async createOrganizationRole(
    @Param('orgId') organizationId: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() roleData: Omit<CreateOrganizationRoleRequest, 'organizationId'>
  ) {
    try {
      this.logger.log(
        `Create organization role request: ${roleData.name} for org: ${organizationId}`
      );

      // Convert the organization role request to the format expected by createRoleWithDetails
      const createRoleRequest: CreateRoleWithDetailsRequest = {
        name: roleData.name,
        departments: [], // Empty departments for organization roles
        modules: roleData.permissions.map((permission) => ({
          id: permission.id,
          features: permission.features.map((feature) => ({
            id: feature.id,
            feature: feature.feature,
            permissions: feature.permissions,
            subFeatures:
              feature.subFeatures?.map((subFeature) => ({
                id: subFeature.id,
                subFeature: subFeature.subFeature,
                permissions: subFeature.permissions,
              })) || [],
          })),
        })),
        getArgs: {
          userId,
          roleName: roles[0],
          ipAddress,
          userAgent,
        },
      };

      return await firstValueFrom(
        this.authService.createRoleWithDetails(createRoleRequest)
      );
    } catch (error) {
      this.logger.error(
        `Create organization role failed: ${error.message}`,
        error.stack
      );
      throw new HttpException(
        'Failed to create organization role',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Get('organizations/:orgId/roles')
  async getOrganizationRoles(
    @Param('orgId') organizationId: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Query('includeSystemRoles') includeSystemRoles?: boolean,
    @Query('page') page?: number,
    @Query('limit') limit?: number
  ) {
    try {
      this.logger.log(
        `Get organization roles request for org: ${organizationId}`
      );

      // Use the same getRolesWithDetails method as the /roles endpoint to ensure consistent response format
      return await firstValueFrom(
        this.authService.getRolesWithDetails({
          userId,
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error(
        `Get organization roles failed: ${error.message}`,
        error.stack
      );
      throw new HttpException(
        'Failed to get organization roles',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Get('roles')
  async listRoles(
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Query('organizationId') organizationId?: number,
    @Query('includeSystemRoles') includeSystemRoles?: boolean,
    @Query('page') page?: number,
    @Query('limit') limit?: number
  ) {
    try {
      this.logger.log(`List roles request`);

      return await firstValueFrom(
        this.authService.listRoles({
          organizationId,
          includeSystemRoles: includeSystemRoles === true,
          page: page || 1,
          limit: limit || 10,
        })
      );
    } catch (error) {
      this.logger.error(`List roles failed: ${error.message}`, error.stack);
      throw new HttpException('Failed to list roles', HttpStatus.BAD_REQUEST);
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Get('roles/:id')
  async getRole(
    @Param('id') id: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log(`Get role request: ${id}`);

      return await firstValueFrom(this.authService.getRole({ id }));
    } catch (error) {
      this.logger.error(`Get role failed: ${error.message}`, error.stack);
      throw new HttpException('Failed to get role', HttpStatus.BAD_REQUEST);
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Put('roles/:id')
  async updateRole(
    @Param('id') id: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() roleData: Omit<UpdateRoleRequest, 'id'>
  ) {
    try {
      this.logger.log(`Update role request: ${id}`);

      const response = await firstValueFrom(
        this.authService.updateRoleNew({
          ...roleData,
          id,
        })
      );
      return {
        ...response,
        role: {
          id: id,
          role: roleData,
        },
      };
    } catch (error) {
      this.logger.error(`Update role failed: ${error.message}`, error.stack);
      throw new HttpException('Failed to update role', HttpStatus.BAD_REQUEST);
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Delete('roles/:id')
  async deleteOrganizationRole(
    @Param('id') id: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log(`Delete role request: ${id}`);

      return await firstValueFrom(
        this.authService.deleteRoleNew({
          id,
          getArgs: {
            userId,
            roleName: roles[0],
            ipAddress,
            userAgent,
          },
        })
      );
    } catch (error) {
      this.logger.error(`Delete role failed: ${error.message}`, error.stack);
      throw new HttpException('Failed to delete role', HttpStatus.BAD_REQUEST);
    }
  }
  // #endregion

  // ─── Organization-specific Department Management endpoints ─────────────────────────
  // #region
  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Post('organizations/:orgId/departments')
  async createOrganizationDepartments(
    @Param('orgId') organizationId: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() departmentsData: BulkCreateDepartmentRequest[]
  ) {
    try {
      this.logger.log(
        `Create organization departments request: ${departmentsData.length} departments for org: ${organizationId}`
      );

      // Convert the request to the format expected by the gRPC service
      const departmentsToCreate = departmentsData.map((dept) => ({
        name: dept.name,
        parent: dept.parent || '', // Empty string for no parent
      }));

      return await firstValueFrom(
        this.authService.createDepartment({
          departments: departmentsToCreate,
          organizationId: Number(organizationId),
          userId,
          roleName: roles[0],
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error(
        `Create organization departments failed: ${error.message}`,
        error.stack
      );
      throw new HttpException(
        'Failed to create organization departments',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Get('organizations/:orgId/departments')
  async getOrganizationDepartments(
    @Param('orgId') organizationId: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Query('includeHierarchy') includeHierarchy?: boolean
  ) {
    try {
      this.logger.log(
        `Get organization departments request for org: ${organizationId}`
      );

      return await firstValueFrom(
        this.authService.getOrganizationDepartments({
          organizationId: Number(organizationId),
          includeHierarchy: includeHierarchy === true,
        })
      );
    } catch (error) {
      this.logger.error(
        `Get organization departments failed: ${error.message}`,
        error.stack
      );
      throw new HttpException(
        'Failed to get organization departments',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Get('org-departments')
  async listOrganizationDepartments(
    @Query('organizationId') organizationId: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Query('parentId') parentId?: number,
    @Query('includeChildren') includeChildren?: boolean,
    @Query('page') page?: number,
    @Query('limit') limit?: number
  ) {
    try {
      this.logger.log(`List organization departments request`);

      if (!organizationId) {
        throw new HttpException(
          'Organization ID is required',
          HttpStatus.BAD_REQUEST
        );
      }

      return await firstValueFrom(
        this.authService.listOrgDepartments({
          organizationId,
          parentId,
          includeChildren: includeChildren === true,
          page: page || 1,
          limit: limit || 10,
        })
      );
    } catch (error) {
      this.logger.error(
        `List organization departments failed: ${error.message}`,
        error.stack
      );
      throw new HttpException(
        'Failed to list organization departments',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Get('org-departments/:id')
  async getOrganizationDepartment(
    @Param('id') id: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log(`Get organization department request: ${id}`);

      return await firstValueFrom(this.authService.getOrgDepartment({ id }));
    } catch (error) {
      this.logger.error(
        `Get organization department failed: ${error.message}`,
        error.stack
      );
      throw new HttpException(
        'Failed to get organization department',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Put('org-departments/:id')
  async updateOrganizationDepartment(
    @Param('id') id: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() departmentData: Omit<UpdateOrgDepartmentRequest, 'id'>
  ) {
    try {
      this.logger.log(`Update organization department request: ${id}`);

      return await firstValueFrom(
        this.authService.updateOrgDepartment({
          ...departmentData,
          id,
        })
      );
    } catch (error) {
      this.logger.error(
        `Update organization department failed: ${error.message}`,
        error.stack
      );
      throw new HttpException(
        'Failed to update organization department',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Permissions(UserPermissions.SS_ROLE_AND_PERMISSION_SETUP)
  @Delete('org-departments/:id')
  async deleteOrganizationDepartment(
    @Param('id') id: number,
    @CurrentUser('id') userId: number,
    @CurrentUser('roles') roles: string[],
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log(`Delete organization department request: ${id}`);

      return await firstValueFrom(this.authService.deleteOrgDepartment({ id }));
    } catch (error) {
      this.logger.error(
        `Delete organization department failed: ${error.message}`,
        error.stack
      );
      throw new HttpException(
        'Failed to delete organization department',
        HttpStatus.BAD_REQUEST
      );
    }
  }
  // #endregion

  // ─── Password Reset endpoints ─────────────────────────
  // #region
  @Public()
  @Post('forgot-password')
  async forgotPassword(
    @Body()
    forgotPasswordData: Omit<ForgotPasswordRequest, 'ipAddress' | 'userAgent'>,
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log(
        `Forgot password request for email: ${forgotPasswordData.email}`
      );

      return await firstValueFrom(
        this.authService.forgotPassword({
          ...forgotPasswordData,
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error(
        `Forgot password failed: ${error.message}`,
        error.stack
      );
      throw new HttpException(
        'Failed to process password reset request',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Public()
  @Post('verify-reset-token')
  async verifyResetToken(
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string,
    @Body() verifyTokenData: VerifyResetTokenRequest
  ) {
    try {
      this.logger.log(
        `Verify reset token request for email: ${verifyTokenData.email}`
      );

      return await firstValueFrom(
        this.authService.verifyResetToken(verifyTokenData)
      );
    } catch (error) {
      this.logger.error(
        `Verify reset token failed: ${error.message}`,
        error.stack
      );
      throw new HttpException(
        'Failed to verify reset token',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Public()
  @Post('reset-password')
  async resetPassword(
    @Body()
    resetPasswordData: Omit<ResetPasswordRequest, 'ipAddress' | 'userAgent'>,
    @Ip() ipAddress: string,
    @Headers('user-agent') userAgent: string
  ) {
    try {
      this.logger.log(
        `Reset password request for email: ${resetPasswordData.email}`
      );

      // Validate required fields
      if (
        !resetPasswordData.email ||
        !resetPasswordData.resetToken ||
        !resetPasswordData.newPassword
      ) {
        throw new HttpException(
          'Email, reset token, and new password are required',
          HttpStatus.BAD_REQUEST
        );
      }

      if (resetPasswordData.newPassword !== resetPasswordData.confirmPassword) {
        throw new HttpException(
          'Passwords do not match',
          HttpStatus.BAD_REQUEST
        );
      }

      return await firstValueFrom(
        this.authService.resetPassword({
          ...resetPasswordData,
          ipAddress,
          userAgent,
        })
      );
    } catch (error) {
      this.logger.error(`Reset password failed: ${error.message}`, error.stack);

      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Failed to reset password',
        HttpStatus.BAD_REQUEST
      );
    }
  }
  // #endregion
}
