#!/bin/bash

# Test script for Employee API endpoints with nested structure
# Make sure the services are running before executing this script

BASE_URL="http://auth-api.localhost"
TOKEN=""

echo "=== Employee API Test Script ==="
echo ""

# Function to get auth token
get_auth_token() {
    echo "Getting authentication token..."
    RESPONSE=$(curl -s --location "${BASE_URL}/api/auth/login" \
        --header 'Content-Type: application/json' \
        --data-raw '{
            "email": "<EMAIL>",
            "password": "SuperAdmin123!"
        }')
    
    TOKEN=$(echo $RESPONSE | grep -o '"accessToken":"[^"]*' | cut -d'"' -f4)
    
    if [ -z "$TOKEN" ]; then
        echo "Failed to get authentication token"
        echo "Response: $RESPONSE"
        exit 1
    fi
    
    echo "Token obtained successfully"
    echo ""
}

# Function to test create employee with nested structure
test_create_employee() {
    echo "=== Testing CREATE Employee with Nested Structure ==="
    
    RESPONSE=$(curl -s --location "${BASE_URL}/api/auth/employees" \
        --header 'Content-Type: application/json' \
        --header "Authorization: Bearer ${TOKEN}" \
        --data-raw '{
            "email": "<EMAIL>",
            "password": "TempPass123!",
            "firstName": "John",
            "lastName": "Doe Test",
            "phone": "******-0123",
            "dateOfBirth": "1990-05-15",
            "bloodGroup": "O+",
            "gender": "male",
            "nationality": "American",
            "maritalStatus": "married",
            "joiningDate": "2024-01-15",
            "jobType": "full-time",
            "jobStatus": "active",
            "departmentInfo": {
                "employeeId": "EMP001",
                "department": "Engineering",
                "designation": "Senior Developer",
                "supervisor": "Jane Smith",
                "workLocation": "New York Office"
            },
            "presentAddress": {
                "presentAddress": "123 Main Street",
                "presentCountry": "USA",
                "presentState": "New York",
                "presentCity": "New York",
                "presentPostalCode": "10001"
            },
            "permanentAddress": {
                "permanentAddress": "456 Oak Avenue",
                "permanentCountry": "USA",
                "permanentState": "California",
                "permanentCity": "Los Angeles",
                "permanentPostalCode": "90210"
            },
            "emergencyContact": {
                "emergencyContactType": "Primary Contact",
                "emergencyContactName": "Mary Doe",
                "emergencyContactRelation": "spouse",
                "emergencyContactPhone": "******-0456",
                "emergencyContactEmail": "<EMAIL>",
                "emergencyContactAddress": "123 Main Street, New York, NY 10001"
            },
            "identityInfo": [
                {
                    "type": "NID",
                    "country": "US",
                    "number": "XXXX1100",
                    "issueDate": "2020-01-01",
                    "expiryDate": "2030-01-01"
                }
            ],
            "bankAccount": {
                "accountHolderName": "John Doe Test",
                "accountNumber": "**********",
                "bankName": "Chase Bank",
                "branchName": "Manhattan Branch",
                "routingNumber": "*********",
                "accountType": "checking"
            },
            "socialLinks": {
                "linkedinUrl": "https://linkedin.com/in/johndoe",
                "twitterUrl": "https://twitter.com/johndoe",
                "facebookUrl": "https://facebook.com/johndoe",
                "instagramUrl": "https://instagram.com/johndoe"
            }
        }')
    
    echo "Create Employee Response:"
    echo $RESPONSE | jq '.' 2>/dev/null || echo $RESPONSE
    echo ""
    
    # Extract user ID for further tests
    USER_ID=$(echo $RESPONSE | grep -o '"userId":[0-9]*' | cut -d':' -f2)
    if [ -n "$USER_ID" ]; then
        echo "Created employee with User ID: $USER_ID"
        echo ""
    fi
}

# Function to test get employee
test_get_employee() {
    if [ -z "$USER_ID" ]; then
        echo "Skipping GET test - no user ID available"
        return
    fi
    
    echo "=== Testing GET Employee ==="
    
    RESPONSE=$(curl -s --location "${BASE_URL}/api/auth/employees/${USER_ID}" \
        --header "Authorization: Bearer ${TOKEN}")
    
    echo "Get Employee Response:"
    echo $RESPONSE | jq '.' 2>/dev/null || echo $RESPONSE
    echo ""
}

# Function to test update employee with nested structure
test_update_employee() {
    if [ -z "$USER_ID" ]; then
        echo "Skipping UPDATE test - no user ID available"
        return
    fi
    
    echo "=== Testing UPDATE Employee with Nested Structure ==="
    
    RESPONSE=$(curl -s --location --request PUT "${BASE_URL}/api/auth/employees/${USER_ID}" \
        --header 'Content-Type: application/json' \
        --header "Authorization: Bearer ${TOKEN}" \
        --data-raw '{
            "firstName": "John Updated",
            "lastName": "Doe Test Updated",
            "departmentInfo": {
                "designation": "Lead Developer",
                "supervisor": "Alice Johnson"
            },
            "emergencyContact": {
                "emergencyContactPhone": "******-0999"
            },
            "bankAccount": {
                "accountType": "savings"
            }
        }')
    
    echo "Update Employee Response:"
    echo $RESPONSE | jq '.' 2>/dev/null || echo $RESPONSE
    echo ""
}

# Function to test list employees
test_list_employees() {
    echo "=== Testing LIST Employees ==="
    
    RESPONSE=$(curl -s --location "${BASE_URL}/api/auth/employees" \
        --header "Authorization: Bearer ${TOKEN}")
    
    echo "List Employees Response:"
    echo $RESPONSE | jq '.' 2>/dev/null || echo $RESPONSE
    echo ""
}

# Main execution
echo "Starting Employee API tests..."
echo ""

get_auth_token
test_create_employee
test_get_employee
test_update_employee
test_list_employees

echo "=== Test completed ==="
echo ""
echo "Check the responses above to verify:"
echo "1. Employee creation with nested structure works"
echo "2. All nested data is properly stored in related tables"
echo "3. Employee retrieval returns all data in flat structure"
echo "4. Employee update with nested structure works"
echo "5. List employees includes all employee data"
