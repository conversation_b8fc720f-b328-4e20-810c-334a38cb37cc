syntax = "proto3";
package auth;
import "common.proto";

// New nested message types for structured data
message EmployeeDepartmentData {
  string employeeId = 1;
  string department = 2;
  string designation = 3;
  string supervisor = 4;
  string workLocation = 5;
}

message EmployeeAddressData {
  string presentAddress = 1;
  string presentCountry = 2;
  string presentState = 3;
  string presentCity = 4;
  string presentPostalCode = 5;
  string permanentAddress = 6;
  string permanentCountry = 7;
  string permanentState = 8;
  string permanentCity = 9;
  string permanentPostalCode = 10;
}

message EmployeeEmergencyContactData {
  string emergencyContactType = 1;
  string emergencyContactName = 2;
  string emergencyContactRelation = 3;
  string emergencyContactPhone = 4;
  string emergencyContactEmail = 5;
  string emergencyContactAddress = 6;
}

message EmployeeIdentityData {
  string type = 1;
  string country = 2;
  string number = 3;
  string issueDate = 4;
  string expiryDate = 5;
}

message EmployeeBankAccountData {
  string accountHolderName = 1;
  string accountNumber = 2;
  string bankName = 3;
  string branchName = 4;
  string routingNumber = 5;
  string accountType = 6;
}

message EmployeeSocialLinksData {
  string linkedinUrl = 1;
  string twitterUrl = 2;
  string facebookUrl = 3;
  string instagramUrl = 4;
}

// Employee Management Messages
message CreateEmployeeRequest {
  // User creation fields
  string email = 1;
  string password = 2;

  // Personal Information
  string firstName = 3;
  string lastName = 4;
  string phone = 5;
  string dateOfBirth = 6;
  string bloodGroup = 7;
  string gender = 8;
  string nationality = 9;
  string maritalStatus = 10;

  // Employment Information
  string joiningDate = 11;
  string jobType = 12;
  string jobStatus = 13;

  // Structured data
  EmployeeDepartmentData departmentInfo = 14;
  EmployeeAddressData presentAddress = 15;
  EmployeeAddressData permanentAddress = 16;
  EmployeeEmergencyContactData emergencyContact = 17;
  repeated EmployeeIdentityData identityInfo = 18;
  EmployeeBankAccountData bankAccount = 19;
  EmployeeSocialLinksData socialLinks = 20;

  // Organization context
  int64 organizationId = 21;

  // Audit fields
  int64 requestUserId = 22;
  string roleName = 23;
  string ipAddress = 24;
  string userAgent = 25;
}

message UpdateEmployeeRequest {
  int64 id = 1;

  // Personal Information
  string firstName = 2;
  string lastName = 3;
  string email = 4;
  string phone = 5;
  string dateOfBirth = 6;
  string bloodGroup = 7;
  string gender = 8;
  string nationality = 9;
  string maritalStatus = 10;

  // Employment Information
  string joiningDate = 11;
  string jobType = 12;
  string jobStatus = 13;

  // Structured data
  EmployeeDepartmentData departmentInfo = 14;
  EmployeeAddressData presentAddress = 15;
  EmployeeAddressData permanentAddress = 16;
  EmployeeEmergencyContactData emergencyContact = 17;
  repeated EmployeeIdentityData identityInfo = 18;
  EmployeeBankAccountData bankAccount = 19;
  EmployeeSocialLinksData socialLinks = 20;

  // Organization context
  int64 organizationId = 21;

  // Audit fields
  int64 requestUserId = 22;
  string roleName = 23;
  string ipAddress = 24;
  string userAgent = 25;
}

message GetEmployeeRequest {
  int64 id = 1;
  int64 organizationId = 2;
  int64 requestUserId = 3;
  string roleName = 4;
  string ipAddress = 5;
  string userAgent = 6;
}

message ListEmployeesRequest {
  int32 page = 1;
  int32 limit = 2;
  string search = 3;
  string jobType = 4;
  string jobStatus = 5;
  int64 organizationId = 6;
  int64 requestUserId = 7;
  string roleName = 8;
  string ipAddress = 9;
  string userAgent = 10;
}

message RemoveEmployeeRequest {
  int64 id = 1;
  int64 organizationId = 2;
  int64 requestUserId = 3;
  string roleName = 4;
  string ipAddress = 5;
  string userAgent = 6;
}

message EmployeeResponse {
  bool success = 1;
  string message = 2;
  EmployeeInfo employee = 3;
}

message ListEmployeesResponse {
  bool success = 1;
  string message = 2;
  repeated EmployeeInfo employees = 3;
  int32 total = 4;
  int32 page = 5;
  int32 limit = 6;
}

message RemoveEmployeeResponse {
  bool success = 1;
  string message = 2;
}

message EmployeeInfo {
  int64 userId = 1;
  string email = 2;

  // Personal Information
  string firstName = 3;
  string lastName = 4;
  string phone = 5;
  string dateOfBirth = 6;
  string bloodGroup = 7;
  string gender = 8;
  string nationality = 9;
  string maritalStatus = 10;

  // Employment Information
  string joiningDate = 11;
  string jobType = 12;
  string jobStatus = 13;

  // Department Information
  string employeeId = 14;
  string department = 15;
  string designation = 16;
  string supervisor = 17;
  string workLocation = 18;

  // Address Information
  string presentAddress = 19;
  string presentCountry = 20;
  string presentState = 21;
  string presentCity = 22;
  string presentPostalCode = 23;
  string permanentAddress = 24;
  string permanentCountry = 25;
  string permanentState = 26;
  string permanentCity = 27;
  string permanentPostalCode = 28;

  // Emergency Contact
  string emergencyContactType = 29;
  string emergencyContactName = 30;
  string emergencyContactRelation = 31;
  string emergencyContactPhone = 32;
  string emergencyContactEmail = 33;
  string emergencyContactAddress = 34;

  // Identity Information
  string identityType = 35;
  string identityCountry = 36;
  string identityNumber = 37;
  string identityIssueDate = 38;
  string identityExpiryDate = 39;

  // Bank Account Information
  string accountHolderName = 40;
  string accountNumber = 41;
  string bankName = 42;
  string branchName = 43;
  string routingNumber = 44;
  string accountType = 45;

  // Social Profile
  string linkedinUrl = 46;
  string twitterUrl = 47;
  string facebookUrl = 48;
  string instagramUrl = 49;

  // Organization
  int64 organizationId = 50;

  // Timestamps
  string createdAt = 51;
  string updatedAt = 52;
}

message EmployeeAddressInfo {
  int64 id = 1;
  int64 userId = 2;
  string addressType = 3;
  string addressLine = 4;
  string country = 5;
  string state = 6;
  string city = 7;
  string postalCode = 8;
}

message EmployeeEmergencyContactInfo {
  int64 id = 1;
  int64 userId = 2;
  string category = 3;
  string name = 4;
  string relationship = 5;
  string address = 6;
  string phoneNumber = 7;
  string email = 8;
}

message EmployeeIdentityDocInfo {
  int64 id = 1;
  int64 userId = 2;
  string docType = 3;
  string nationality = 4;
  string issueDate = 5;
  string expiryDate = 6;
}

message EmployeeBankAccountInfo {
  int64 id = 1;
  int64 userId = 2;
  string accountHolder = 3;
  string accountNumber = 4;
  string bankName = 5;
  string branchName = 6;
}
