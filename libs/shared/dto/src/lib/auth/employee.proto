syntax = "proto3";
package auth;
import "common.proto";

// New nested message types for structured data
message EmployeeDepartmentData {
  string employeeId = 1;
  string department = 2;
  string designation = 3;
  string supervisor = 4;
  string workLocation = 5;
}

message EmployeeAddressData {
  string presentAddress = 1;
  string presentCountry = 2;
  string presentState = 3;
  string presentCity = 4;
  string presentPostalCode = 5;
  string permanentAddress = 6;
  string permanentCountry = 7;
  string permanentState = 8;
  string permanentCity = 9;
  string permanentPostalCode = 10;
}

message EmployeeEmergencyContactData {
  string emergencyContactType = 1;
  string emergencyContactName = 2;
  string emergencyContactRelation = 3;
  string emergencyContactPhone = 4;
  string emergencyContactEmail = 5;
  string emergencyContactAddress = 6;
}

message EmployeeIdentityData {
  string type = 1;
  string country = 2;
  string number = 3;
  string issueDate = 4;
  string expiryDate = 5;
}

message EmployeeBankAccountData {
  string accountHolderName = 1;
  string accountNumber = 2;
  string bankName = 3;
  string branchName = 4;
  string routingNumber = 5;
  string accountType = 6;
}

message EmployeeSocialLinksData {
  string linkedinUrl = 1;
  string twitterUrl = 2;
  string facebookUrl = 3;
  string instagramUrl = 4;
}

// Employee Management Messages
message CreateEmployeeRequest {
  // User creation fields
  string email = 1;
  string password = 2;
  string name = 3;
  string phone = 4;
  string departmentName = 5;
  string organizationName = 6;
  string employeeRoleName = 7;

  // Personal Information
  string firstName = 8;
  string lastName = 9;
  string dateOfBirth = 10;
  string bloodGroup = 11;
  string gender = 12;
  string nationality = 13;
  string maritalStatus = 14;

  // Employment Information
  string joiningDate = 15;
  string jobType = 16;
  string jobStatus = 17;

  // Nested structured data (new format)
  EmployeeDepartmentData departmentInfo = 18;
  EmployeeAddressData presentAddress = 19;
  EmployeeAddressData permanentAddress = 20;
  EmployeeEmergencyContactData emergencyContact = 21;
  repeated EmployeeIdentityData identityInfo = 22;
  EmployeeBankAccountData bankAccount = 23;
  EmployeeSocialLinksData socialLinks = 24;

  // Legacy flat fields for backward compatibility
  string employeeId = 25;
  string department = 26;
  string designation = 27;
  string reportingTo = 28;
  string workLocation = 29;
  string presentAddressFlat = 30;
  string presentCountry = 31;
  string presentState = 32;
  string presentCity = 33;
  string presentPostalCode = 34;
  string permanentAddressFlat = 35;
  string permanentCountry = 36;
  string permanentStateFlat = 37;
  string permanentCityFlat = 38;
  string permanentPostalCode = 39;
  string emergencyContactName = 40;
  string emergencyContactRelation = 41;
  string emergencyContactPhone = 42;
  string emergencyContactEmail = 43;
  string emergencyContactAddress = 44;
  string securityType = 45;
  string securityMaturity = 46;
  string securityBankCode = 47;
  string securityExpiryDate = 48;
  string accountHolderName = 49;
  string accountNumber = 50;
  string bankName = 51;
  string branchName = 52;
  string routingNumber = 53;
  string accountType = 54;
  string linkedinUrl = 55;
  string twitterUrl = 56;
  string facebookUrl = 57;
  string instagramUrl = 58;

  // Legacy fields
  int64 userId = 59;
  int64 agencyId = 60;
  string orgId = 61;
  repeated EmployeeAddressInfo addresses = 62;
  repeated EmployeeEmergencyContactInfo emergencyContacts = 63;
  repeated EmployeeIdentityDocInfo identityDocs = 64;
  repeated EmployeeBankAccountInfo bankAccounts = 65;

  // Audit fields
  int64 requestUserId = 66;
  string roleName = 67;
  string ipAddress = 68;
  string userAgent = 69;
}

message UpdateEmployeeRequest {
  int64 id = 1;

  // Personal Information
  string firstName = 2;
  string lastName = 3;
  string email = 4;
  string phone = 5;
  string dateOfBirth = 6;
  string bloodGroup = 7;
  string gender = 8;
  string nationality = 9;
  string maritalStatus = 10;

  // Employment Information
  string joiningDate = 11;
  string jobType = 12;
  string jobStatus = 13;

  // Nested structured data (new format)
  EmployeeDepartmentData departmentInfo = 14;
  EmployeeAddressData presentAddress = 15;
  EmployeeAddressData permanentAddress = 16;
  EmployeeEmergencyContactData emergencyContact = 17;
  repeated EmployeeIdentityData identityInfo = 18;
  EmployeeBankAccountData bankAccount = 19;
  EmployeeSocialLinksData socialLinks = 20;

  // Legacy flat fields for backward compatibility
  string employeeId = 21;
  string department = 22;
  string designation = 23;
  string reportingTo = 24;
  string workLocation = 25;
  string presentAddressFlat = 26;
  string presentCountry = 27;
  string presentState = 28;
  string presentCity = 29;
  string presentPostalCode = 30;
  string permanentAddressFlat = 31;
  string permanentCountry = 32;
  string permanentStateFlat = 33;
  string permanentCityFlat = 34;
  string permanentPostalCode = 35;
  string emergencyContactName = 36;
  string emergencyContactRelation = 37;
  string emergencyContactPhone = 38;
  string emergencyContactEmail = 39;
  string emergencyContactAddress = 40;
  string securityType = 41;
  string securityMaturity = 42;
  string securityBankCode = 43;
  string securityExpiryDate = 44;
  string accountHolderName = 45;
  string accountNumber = 46;
  string bankName = 47;
  string branchName = 48;
  string routingNumber = 49;
  string accountType = 50;
  string linkedinUrl = 51;
  string twitterUrl = 52;
  string facebookUrl = 53;
  string instagramUrl = 54;

  // Legacy fields
  int64 agencyId = 55;
  string orgId = 56;
  repeated EmployeeAddressInfo addresses = 57;
  repeated EmployeeEmergencyContactInfo emergencyContacts = 58;
  repeated EmployeeIdentityDocInfo identityDocs = 59;
  repeated EmployeeBankAccountInfo bankAccounts = 60;

  // Audit fields
  int64 requestUserId = 61;
  string roleName = 62;
  string ipAddress = 63;
  string userAgent = 64;
}

message GetEmployeeRequest {
  int64 id = 1;
  int64 requestUserId = 2;
  string roleName = 3;
  string ipAddress = 4;
  string userAgent = 5;
}

message ListEmployeesRequest {
  int32 page = 1;
  int32 limit = 2;
  string search = 3;
  string jobType = 4;
  string jobStatus = 5;
  int64 agencyId = 6;
  string orgId = 7;
  int64 requestUserId = 8;
  string roleName = 9;
  string ipAddress = 10;
  string userAgent = 11;
}

message RemoveEmployeeRequest {
  int64 id = 1;
  int64 requestUserId = 2;
  string roleName = 3;
  string ipAddress = 4;
  string userAgent = 5;
}

message EmployeeResponse {
  bool success = 1;
  string message = 2;
  EmployeeInfo employee = 3;
}

message ListEmployeesResponse {
  bool success = 1;
  string message = 2;
  repeated EmployeeInfo employees = 3;
  int32 total = 4;
  int32 page = 5;
  int32 limit = 6;
}

message RemoveEmployeeResponse {
  bool success = 1;
  string message = 2;
}

message EmployeeInfo {
  int64 userId = 1;

  // Personal Information
  string lastName = 2;
  string firstName = 3;
  string phone = 4;
  string dateOfBirth = 5;
  string bloodGroup = 6;
  string gender = 7;
  string nationality = 8;
  string maritalStatus = 9;

  // Employment Information
  string joiningDate = 10;
  string jobType = 11;
  string jobStatus = 12;

  // Department Information
  string employeeId = 13;
  string department = 14;
  string designation = 15;
  string supervisor = 16;
  string workLocation = 17;

  // Address Information
  string presentAddress = 18;
  string presentCountry = 19;
  string presentState = 20;
  string presentCity = 21;
  string presentPostalCode = 22;
  string permanentAddress = 23;
  string permanentCountry = 24;
  string permanentState = 25;
  string permanentCity = 26;
  string permanentPostalCode = 27;

  // Emergency Contact
  string emergencyContactType = 28;
  string emergencyContactName = 29;
  string emergencyContactRelation = 30;
  string emergencyContactPhone = 31;
  string emergencyContactEmail = 32;
  string emergencyContactAddress = 33;

  // Identity Information
  string identityType = 34;
  string identityCountry = 35;
  string identityNumber = 36;
  string identityIssueDate = 37;
  string identityExpiryDate = 38;

  // Bank Account Information
  string accountHolderName = 39;
  string accountNumber = 40;
  string bankName = 41;
  string branchName = 42;
  string routingNumber = 43;
  string accountType = 44;

  // Social Profile
  string linkedinUrl = 45;
  string twitterUrl = 46;
  string facebookUrl = 47;
  string instagramUrl = 48;

  // Legacy fields
  int64 agencyId = 49;
  string orgId = 50;
  repeated EmployeeAddressInfo addresses = 51;
  repeated EmployeeEmergencyContactInfo emergencyContacts = 52;
  repeated EmployeeIdentityDocInfo identityDocs = 53;
  repeated EmployeeBankAccountInfo bankAccounts = 54;

  string createdAt = 55;
  string updatedAt = 56;
}

message EmployeeAddressInfo {
  int64 id = 1;
  int64 userId = 2;
  string addressType = 3;
  string addressLine = 4;
  string country = 5;
  string state = 6;
  string city = 7;
  string postalCode = 8;
}

message EmployeeEmergencyContactInfo {
  int64 id = 1;
  int64 userId = 2;
  string category = 3;
  string name = 4;
  string relationship = 5;
  string address = 6;
  string phoneNumber = 7;
  string email = 8;
}

message EmployeeIdentityDocInfo {
  int64 id = 1;
  int64 userId = 2;
  string docType = 3;
  string nationality = 4;
  string issueDate = 5;
  string expiryDate = 6;
}

message EmployeeBankAccountInfo {
  int64 id = 1;
  int64 userId = 2;
  string accountHolder = 3;
  string accountNumber = 4;
  string bankName = 5;
  string branchName = 6;
}
