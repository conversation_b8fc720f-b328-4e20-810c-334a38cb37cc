{"success": true, "message": "Employees retrieved successfully", "employees": [{"userId": 1, "lastName": "Admin", "firstName": "Super", "dateOfBirth": "1990-01-01T00:00:00.000Z", "bloodGroup": "O+", "gender": "Other", "joiningDate": "2025-07-05T05:59:23.421Z", "jobType": "Full-time", "jobStatus": "Active", "orgId": "1", "addresses": [{"id": 1, "userId": 1, "addressType": "Work", "addressLine": "ApplyGoal Headquarters", "country": "Canada", "state": "Ontario", "city": "Toronto", "postalCode": "M5V 3A8"}], "emergencyContacts": [{"id": 1, "userId": 1, "relationship": "Organization"}], "identityDocs": [{"id": 1, "userId": 1, "docType": "Admin ID", "nationality": "Canadian", "issueDate": "2025-07-05T05:59:23.426Z", "expiryDate": "2035-07-03T05:59:23.426Z"}], "bankAccounts": [{"id": 1, "userId": 1, "accountHolder": "Super Admin", "accountNumber": "ADMIN-001", "bankName": "ApplyGoal Bank", "branchName": "Main Branch"}], "createdAt": "Sat Jul 05 2025 05:59:23 GMT+0000 (Coordinated Universal Time)", "updatedAt": "Sat Jul 05 2025 05:59:23 GMT+0000 (Coordinated Universal Time)"}, {"userId": 2, "lastName": "<PERSON><PERSON>", "firstName": "<PERSON>", "phone": "******-0123", "dateOfBirth": "1990-05-15T00:00:00.000Z", "bloodGroup": "B+", "gender": "male", "nationality": "British", "maritalStatus": "married", "joiningDate": "2024-01-15T00:00:00.000Z", "jobType": "full-time", "jobStatus": "active", "employeeId": "EMP001", "department": "IT", "designation": "Senior Developer", "supervisor": "<PERSON>", "workLocation": "New York Office", "presentAddress": "123 Main Street", "presentCountry": "USA", "presentState": "New York", "presentCity": "New York", "presentPostalCode": "10001", "permanentAddress": "456 Oak Avenue", "permanentCountry": "USA", "permanentState": "California", "permanentCity": "Los Angeles", "permanentPostalCode": "90210", "emergencyContactType": "Primary Contact", "emergencyContactName": "<PERSON>", "emergencyContactRelation": "spouse", "emergencyContactPhone": "******-0456", "emergencyContactEmail": "<EMAIL>", "emergencyContactAddress": "123 Main Street, New York, NY 10001", "accountHolderName": "<PERSON>", "accountNumber": "**********", "bankName": "Chase Bank", "branchName": "Manhattan Branch", "routingNumber": "*********", "accountType": "checking", "linkedinUrl": "https://linkedin.com/in/johndoe", "twitterUrl": "https://twitter.com/johndoe", "facebookUrl": "https://facebook.com/johndoe", "instagramUrl": "https://instagram.com/johndoe", "agencyId": 0, "orgId": "", "addresses": [{"id": 8, "userId": 2, "addressType": "present", "addressLine": "123 Main Street", "country": "USA", "state": "New York", "city": "New York", "postalCode": "10001"}, {"id": 9, "userId": 2, "addressType": "permanent", "addressLine": "456 Oak Avenue", "country": "USA", "state": "California", "city": "Los Angeles", "postalCode": "90210"}], "emergencyContacts": [{"id": 5, "userId": 2, "category": "Primary Contact", "name": "<PERSON>", "relationship": "spouse", "address": "123 Main Street, New York, NY 10001", "phoneNumber": "******-0456", "email": "<EMAIL>"}], "identityDocs": [{"id": 5, "userId": 2, "docType": "NID", "nationality": "US"}], "bankAccounts": [{"id": 5, "userId": 2, "accountHolder": "<PERSON>", "accountNumber": "**********", "bankName": "Chase Bank", "branchName": "Manhattan Branch"}], "createdAt": "Sat Jul 05 2025 05:59:57 GMT+0000 (Coordinated Universal Time)", "updatedAt": "Sat Jul 05 2025 09:13:34 GMT+0000 (Coordinated Universal Time)"}, {"userId": 3, "lastName": "<PERSON><PERSON>", "firstName": "<PERSON>", "phone": "******-0123", "dateOfBirth": "1990-05-15T00:00:00.000Z", "bloodGroup": "O+", "gender": "male", "nationality": "American", "maritalStatus": "married", "agencyId": 0, "orgId": "1", "departmentInfo": {"employeeId": "EMP001", "department": "Engineering", "designation": "Senior Developer", "supervisor": "<PERSON>", "workLocation": "New York Office"}, "addresses": [{"id": 4, "userId": 3, "addressType": "present", "addressLine": "123 Main Street", "country": "USA", "state": "New York", "city": "New York", "postalCode": "10001"}, {"id": 5, "userId": 3, "addressType": "permanent", "addressLine": "456 Oak Avenue", "country": "USA", "state": "California", "city": "Los Angeles", "postalCode": "90210"}], "emergencyContacts": [{"id": 3, "userId": 3, "category": "Primary Contact", "name": "<PERSON>", "relationship": "spouse", "address": "123 Main Street, New York, NY 10001", "phoneNumber": "******-0456", "email": "<EMAIL>"}], "identityDocs": [{"id": 3, "userId": 3, "docType": "NID", "nationality": "US", "number": "XXXX1100", "issueDate": "2020-01-01T00:00:00.000Z", "expiryDate": "2030-01-01T00:00:00.000Z"}], "bankAccounts": [{"id": 3, "userId": 3, "accountHolder": "<PERSON>", "accountNumber": "**********", "bankName": "Chase Bank", "branchName": "Manhattan Branch", "routingNumber": "*********"}], "createdAt": "Sat Jul 05 2025 09:10:59 GMT+0000 (Coordinated Universal Time)", "updatedAt": "Sat Jul 05 2025 09:10:59 GMT+0000 (Coordinated Universal Time)"}], "total": 3, "page": 1, "limit": 10}